# Vibe Coder: Developing Apps with AI

## Overview

This documentation explains how to develop mobile applications using the Vid Flutter Base Project with AI assistance. By following this guide, you can create a fully functional mobile app without writing code yourself. The process involves completing initial setup tasks manually, then using AI to generate and implement app-specific features.

## About the Vid Flutter Base Project

The Vid Flutter Base Project is a comprehensive starter template that includes:

- **Pre-built UI Components**: Modern, customizable interface elements
- **Multi-language Support**: Built-in localization system
- **Navigation System**: Structured routing between screens
- **State Management**: Efficient data handling throughout the app
- **Local Database**: Secure storage for user data
- **Service Integrations**: Firebase, AdMob, and RevenueCat ready to use
- **Theming System**: Consistent design across the app

This foundation allows you to focus on your app's unique features rather than implementing common functionality from scratch.

## Prerequisites

- Basic understanding of mobile app concepts
- Access to an AI coding assistant (like Cascade or GitHub Copilot)
- Flutter development environment set up on your machine
- A copy of the Vid Flutter Base Project

## Development Workflow

### Step 1: Complete Initial Setup Tasks

Before using AI to develop your app, you must complete the initial setup tasks manually. These tasks are listed in the `project_todo.md` file under the **Initial Setup Tasks** section:

1. **Project Configuration**
   - Update package name
   - Configure app constants
   - Update localization

2. **Visual Assets**
   - Generate app icon
   - Generate native splash screen

3. **Service Integrations**
   - Set up Firebase
   - Configure or disable Firestore for AI
   - Set up AdMob
   - Set up RevenueCat

These tasks require manual intervention because they involve system-specific configurations and external service accounts that AI cannot access directly.

### Step 2: Generate Project-Specific Tasks

Once initial setup is complete, use the `generate_todo_prompt.md` file to have AI generate tasks specific to your app:

1. Open the `generate_todo_prompt.md` file and fill in your information in the **User Input** section:
   ```
   **App Idea**: [Your app description]
   **Feature Requests**: [List of features or "YOU DECIDE" if you want the AI to suggest features]
   ```

2. Copy the entire content of the `generate_todo_prompt.md` file (including your filled-in information) and paste it to the AI assistant.

3. The AI will automatically understand the prompt and update the `project_todo.md` file with a list of tasks in the **Project Specific Tasks** section, organized by priority and implementation order. You don't need to provide any additional instructions - the prompt already contains all the necessary guidance for the AI.

### Step 3: Complete Tasks One by One

With your task list ready, use the `complete_todo.md` file to implement each task:

1. Copy the entire content of the `complete_todo.md` file and paste it to the AI assistant.

2. The AI will automatically:
   - Identify the first uncompleted task (marked with `- [ ]`) in the **Project Specific Tasks** section
   - Implement the necessary code and functionality
   - Update the task in `project_todo.md` to mark it as completed (`- [x]`)
   - Explain what was done in non-technical terms

3. After each task is completed, simply copy and paste the `complete_todo.md` content to the AI again to complete the next task. The prompt already contains all the necessary instructions for the AI to identify and complete the next task in sequence.

4. Repeat this process until all tasks are completed.

## Example Workflow

### Initial Setup

1. Clone the Vid Flutter Base Project
2. Complete the initial setup tasks in `project_todo.md`

### Generate Tasks for an AI Paraphrase App

1. Open `generate_todo_prompt.md` and fill in your information:

```
**App Idea**: An AI-powered text paraphrasing app that can rewrite text in different styles and tones.

**Feature Requests**: 
1. Text input area for original content
2. Multiple paraphrasing styles (formal, casual, creative)
3. History of previous paraphrases
4. Premium features with subscription
```

2. Copy the entire content of the `generate_todo_prompt.md` file (including your filled-in information) and paste it to the AI assistant.

3. The AI will automatically update the `project_todo.md` file with tasks specific to your AI Paraphrase App.

### Complete Tasks

1. After the AI updates `project_todo.md`, copy the entire content of the `complete_todo.md` file and paste it to the AI assistant.

2. The AI will complete the first uncompleted task in the Project Specific Tasks section.

3. After the task is completed, copy and paste the `complete_todo.md` content to the AI again to complete the next task.

4. Repeat until all tasks are completed.

## Best Practices

### Providing Clear Instructions

- Be specific about what you want
- Provide examples when possible
- Ask for explanations if you don't understand something

### Testing Your App

- Test each feature after it's implemented
- Provide feedback to the AI if something isn't working as expected
- Ask the AI to fix any issues that arise

### Iterative Development

- Start with core functionality
- Add more complex features later
- Refine the user experience based on testing

## Localization

The Vid Flutter Base Project includes built-in support for multiple languages. When the AI implements features, it will automatically:

1. Add new text strings to all language files in `assets/translations/`
2. Update the `LocalizationKeys` enum in `i10n_constants.dart`
3. Use the `tr()` extension method to access translations in the UI

You can ask the AI to add support for additional languages at any time.

## Conclusion

By following this workflow, you can develop a fully functional mobile app without writing code yourself. The combination of the Vid Flutter Base Project and AI assistance makes app development accessible to everyone, regardless of technical background.

The key benefits of this approach include:

- **Speed**: Develop apps much faster than traditional coding
- **Accessibility**: Create professional apps without programming knowledge
- **Quality**: Leverage best practices built into the base project
- **Flexibility**: Customize every aspect of your app through AI instructions

Remember that while AI can handle most implementation details, complex features or integrations might still require professional developer input.

## Getting Help

If you encounter any issues during development:

1. Ask the AI to explain what's happening in simple terms
2. Request the AI to suggest solutions or alternatives
3. For complex problems, consider consulting with a Flutter developer

Happy app building!