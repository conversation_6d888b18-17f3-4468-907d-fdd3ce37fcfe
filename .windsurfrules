# Vid Base Project Windsurf Rules
These rules ensure consistent development, maintainability, and performance optimization in the Vid Base Project.

## Project Architecture
- Organize features in `lib/features` with a feature-first approach.
- Place shared functionality in `lib/product`.
- Structure codebase into `bottom_navigation_screens`, `intro_screens`, and `other_screens`.

## Code Structure
- Use mixins for view logic (e.g., `*_mixin.dart`) to separate UI from business logic.
- Name files consistently: `*_view.dart` for UI, `*_mixin.dart` for logic.
- Implement state management using Cubit for simple cases or BLoC for complex cases with Freezed for immutable states.
- Follow existing directory structure and naming patterns for new files.

## Performance Optimization
- Use `const` constructors for widgets to minimize rebuilds.
- Avoid `setState()` use BLoC/Cubit for state management.

## Firebase Integration
- Use `VidFirebaseWrapper` for all Firebase interactions (Analytics, Firestore, Crashlytics).
- Handle exceptions in Firebase calls.

## State Management
- Use Cubit for state management.
- Implement Freezed for immutable state classes.
- Prefer feature-specific state over global state.
- Follow existing feature patterns for consistency.

## Navigation
- Use `GoRouter` for all navigation.
- Organize routes into `InitialRoute`, `MainRoute`, and `OtherRoute`.
- Use context extension methods for navigation.
- Add new routes to the appropriate route paths enum.

## Local Database
- Use `LocalDbManager` for all local storage operations.
- Use Freezed models with proper serialization for database entities.
- Implement error handling for database operations.
- Use transactions for multi-record modifications.

## Vid Packages
- Use Vid packages where possible.
- Avoid modifying Vid package code directly.

## Environment Configuration
- Access environment variables via `app_constants.dart`.
- Use `.env.development` for development and `.env.production` for production.

## Localization
- Organize localization keys hierarchically to match feature or screen structure.
- Never use hardcoded strings in UI always use localization keys.
- Group related localization keys in json files by feature or screen.
- When adding a new feature, create all necessary localization keys before implementing UI.
- Ensure all user-facing text is localizable, including error messages and tooltips.
- Add translations to all language files in `assets/translations/`.
- Make sure keys in all json files are matched with `LocalizationKeys` enum and with each other.
- Update `LocalizationKeys` enum in `i10n_constants.dart`.
- Use `tr()` extension method of `LocalizationKeys` for accessing translations.

## UI Components
- Use ForUI components for consistent design. (https://forui.dev/docs)
- For dialogs and bottom sheets use `vid_ui` package.
- Follow the theming system for colors and typography.
- Access colors and typography via context extensions in `vid_ui` package.

## Dependency Injection
- Use `GetIt` for dependency injection.
- Register services in `app_initializer.dart` for app startup or `splash_mixin.dart` for others.
- Use lazy initialization for non-critical services.
- Handle errors during service initialization.

## Error Handling
- Use try-catch blocks for operations that may fail.
- Provide user-friendly error messages.
- Log errors to Firebase Crashlytics.
- Follow existing error handling patterns.
- Avoid silent failures.