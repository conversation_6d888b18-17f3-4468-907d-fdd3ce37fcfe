import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:vid_base_project/firebase_options.dart';
import 'package:vid_base_project/product/constants/app_constants.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/local_database/models/app_utilities_model.dart';
import 'package:vid_base_project/product/managers/app_notifier.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_firebase_wrapper/vid_firebase_wrapper.dart';
import 'package:vid_secure_app/vid_secure_app.dart';

// import 'package:vid_local_auth/vid_local_auth.dart';
// import 'package:vid_purchase_manager/vid_purchase_manager.dart';
// import 'package:vid_share_manager/vid_share_manager.dart';

/// Application initializer
class AppInitializer {
  const AppInitializer._();

  /// Initialize all required services and configurations
  static Future<void> initialize() async {
    await VidAppInitializer.initialize();

    await LocalDbManager.instance.init();

    await _initFirebase();

    await _registerAppDependencies();

    // FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

    // PlatformDispatcher.instance.onError = (error, stack) {
    //   FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    //   return true;
    // };
  }

  static Future<void> _registerAppDependencies() async {
    await GetIt.instance
        .registerSingleton<VidSecureAppController>(VidSecureAppController(null))
        .initialize();

    await GetIt.instance
        .registerSingleton<AppNotifier>(AppNotifier())
        .initialize();
  }


  static Future<void> _initFirebase() async {
    await VidFirebaseCoreService.initialize(
      options: DefaultFirebaseOptions.currentPlatform,
      environment: dotenv.env[enviromentNameKey]!,
    );

     GetIt.instance.registerSingleton<VidFirebaseFirestoreService>(
      VidFirebaseFirestoreService(),
    );
    GetIt.instance.registerSingleton<VidFirebaseAnalyticsService>(
      VidFirebaseAnalyticsService(isEnabled: !kDebugMode),
    );


  }
    
}

class AppConfig {
  AppConfig({required this.appUtilities});

  final AppUtilitiesModel appUtilities;
}
