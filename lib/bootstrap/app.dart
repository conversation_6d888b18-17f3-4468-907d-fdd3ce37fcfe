import 'package:flutter/material.dart';
import 'package:vid_base_project/bootstrap/app_initializer.dart';
import 'package:vid_base_project/config/config.dart';
import 'package:vid_base_project/product/common_blocs/index.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_base_project/product/local_database/models/app_utilities_model.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_secure_app/vid_secure_app.dart';

/// Run the application 
Future<void> run() async {
  await AppInitializer.initialize();
  runApp(const MobileApp());
}

/// The main application widget
class MobileApp extends StatelessWidget {
  /// The constructor
  const MobileApp({super.key});


  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => SessionBloc(), lazy: false),
      ],
      child: ValueListenableBuilder<AppUtilitiesModel>(
        valueListenable: appNotifier.appUtil,
        builder: (context, appUtil, child) {  
          return VidApp(
              fallbackLocale: fallbackLocale,
              title: kAppName,
              supportedLocales: supportedLocales,
              routerConfig: AppRouter.router,
              theme: appUtil.themeData(context),
              builder: (context, child) {
                return VidSecureAppWrapper(
                  navigatorKey: AppRouter.parentKey,
                  controller: secureAppController,
                  child: child!,
                );
              },
            );
        },
      ),
    );
  }
}
