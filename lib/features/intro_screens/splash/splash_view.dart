import 'package:flutter/material.dart';
import 'package:vid_base_project/config/config.dart';
import 'package:vid_base_project/features/intro_screens/splash/splash_mixin.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

class SplashView extends StatelessWidget with SplashMixin {
  SplashView({super.key});

  @override
  Widget build(BuildContext context) {
    return VidSplashView(
      logo: const Icon(Icons.text_fields, size: 60),
      title: kAppName,
      description: LocalizationKeys.splashDescription.tr(context: context),
      onInit: () async => initializeApp(context),
      progress: progressNotifier,
      footerText: VidLocalizationKeys.generalSettingsCopyrightText
          .trWithNamedArgs({
            'date': DateTime.now().year.toString(),
            'company': kCompanyName,
          }, context: context),
    );
  }
}
  