import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:vid_ai_service/vid_ai_service.dart';
import 'package:vid_base_project/product/cloud_database/cloud_db_manager.dart';
import 'package:vid_base_project/product/common_blocs/index.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/local_database/models/user_flow_model.dart';
import 'package:vid_base_project/product/managers/quick_actions_manager.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_base_project/product/utils/extensions.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_in_app_review_manager/vid_in_app_review_manager.dart';
import 'package:vid_purchase_manager/vid_purchase_manager.dart';
import 'package:vid_ui/vid_ui.dart';

mixin SplashMixin {
  final ValueNotifier<double> progressNotifier = ValueNotifier<double>(0);

  Future<void> initializeApp(BuildContext context) async {
    try {
      await _initQuickActions();

      await VidPurchaseManager.initialize(
        debugLogsEnabled: true,
        androidApiKey: dotenv.env[revCatApiKeyAndroidEnvKey]!,
        appleApiKey: dotenv.env[revCatApiKeyAppleEnvKey]!,
      );

      progressNotifier.value = 0.2;
      await _initAiService();

      progressNotifier.value = 0.4;

      await initSession(context);

      progressNotifier.value = 0.6;

      await Future<void>.delayed(const Duration(milliseconds: 500));

      progressNotifier.value = 0.8;

      VidInAppReviewManager.initialize(onReviewShowed: (_) {});

      progressNotifier.value = 1;

      await Future<void>.delayed(const Duration(milliseconds: 500));

      await secureAppController.authenticate(context: context, force: true);

      if (context.sessionState.userFlow.isFirstLaunch) {
        context.goNamed(InitialRoutePaths.onboarding.name);
      } else {
        context.goNamed(MainRoutePaths.home.name);
      }
    } on Exception catch (_) {
      unawaited(
        showVidErrorDialog(
          context,
          title: VidLocalizationKeys.actionsTryAgain.tr(),
          barrierDismissible: false,
          onPrimaryButtonPress: () => initializeApp(context),
        ),
      );
    }
  }

  Future<void> _initQuickActions() async {
    if (!GetIt.instance.isRegistered<QuickActionsManager>()) {
      await GetIt.instance.registerSingleton<QuickActionsManager>(
        QuickActionsManager(),
      ).initialize();
    }
  }

  Future<void> _initAiService() async {
    try {
      final didRegister = GetIt.instance.isRegistered<VidAiService>();
      if (!didRegister) {
        final appConfig = await CloudDbManager().getAppConfig();
        GetIt.instance
            .registerSingleton<VidAiService>(VidAiService())
            .init(
              chatGptApiKey: appConfig.apiKey.gptApiKey,
              geminiApiKey: appConfig.apiKey.geminiApiKey,
              deepSeekApiKey: appConfig.apiKey.deepSeekApiKey,
              selectedModel: AIModels.gemini15Flash,
              models: [
                AIModels.deepseekChat,
                AIModels.gpt4O,
                AIModels.gpt35Turbo,
                AIModels.gemini15Flash,
              ],
            );
      }
    } on Exception catch (_) {
      throw ApiException(VidLocalizationKeys.aiMessagesOffline.tr());
    }
  }

  Future<void> initSession(BuildContext context) async {
    // Run independent database calls in parallel
    final results = await Future.wait([
      LocalDbManager.instance.getUserFlow(),
      VidPurchaseManager.instance.hasActiveEntitlement(),
    ]);

    // Extract results
    final userFlow = results[0] as UserFlowModel?;
    final isPremium = results[1] as bool?;

    // Process dependent logic
    final updatedUserFlow = (userFlow ?? const UserFlowModel()).copyWith(
      isUserPremium: isPremium ?? false,
    );

    await LocalDbManager.instance.updateUserFlow(updatedUserFlow);

    final newState = SessionState(userFlow: updatedUserFlow);

    context.sessionEvent(SessionEvent.init(newState));
  }
}
