import 'package:flutter/material.dart';
import 'package:vid_base_project/features/intro_screens/onboarding/onboarding_mixin.dart';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_base_project/product/routing/paths/main_route/main_route_paths.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

class OnboardingView extends StatelessWidget with OnboardingMixin {
  OnboardingView({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: isAnalyzing,
      builder: (context, value, child) {
        if (value) {
          return VidAnalyzeView(
            progressBarCompletedTitle: LocalizationKeys
                .onboardingAnalyzeStepAnalyzedTitle
                .tr(context: context),
            progressBarNotCompletedTitle: LocalizationKeys
                .onboardingAnalyzeStepAnalyzingTitle
                .tr(context: context),
            onTapStart: () => context.goNamed(MainRoutePaths.home.name),
            steps: LocalizationKeys.onboardingAnalyzeStepSteps
                .getIndexedDuoStringList(context)
                .map(
                  (e) => VidAnalyzeStep(
                    id: generateUUIDv4(),
                    icon: FIcons.activity,
                    title: e.title,
                    description: e.description,
                  ),
                )
                .toList(),
          );
        }

        return VidOnboardingView(
          onOpen: () => onOpen(context),
          onboardingPages: [
            OnboardingPageModel(
              title: LocalizationKeys.onboardingWelcomeStepTitle.tr(
                context: context,
              ),
              description: LocalizationKeys.onboardingWelcomeStepDescription.tr(
                context: context,
              ),
              image: const FlutterLogo(size: 300),
            ),
            OnboardingPageModel(
              title: LocalizationKeys.onboardingReadyToUseStepTitle.tr(
                context: context,
              ),
              description: LocalizationKeys.onboardingReadyToUseStepDescription
                  .tr(context: context),
              image: const FlutterLogo(size: 300),
            ),
            OnboardingPageModel(
              title: LocalizationKeys.onboardingPersonalizationStepTitle.tr(
                context: context,
              ),
              description: LocalizationKeys
                  .onboardingPersonalizationStepDescription
                  .tr(context: context),
              onAction: (context) => askForAppTrackingTransparency(),
              customBuilder: (context) {
                final list = LocalizationKeys.onboardingPersonalizationSteps
                    .getIndexedDuoStringList(context);
                return ListView.separated(
                  padding: EdgeInsets.zero,
                  itemCount: list.length,
                  itemBuilder: (context, index) {
                    final item = list[index];
                    return FTile(
                      title: Text(item.title),
                      subtitle: Text(item.description),
                      prefixIcon: const Icon(FIcons.circleCheck),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return kLargeSpacing.heightBox;
                  },
                );
              },
            ),
            OnboardingPageModel(
              title: LocalizationKeys.onboardingRateStepTitle.tr(
                context: context,
              ),
              description: LocalizationKeys.onboardingRateStepDescription.tr(
                context: context,
              ),
              image: const FlutterLogo(size: 300),
              onAction: (context) async => requestReviewIfEligible(),
            ),
          ],
          onFinish: onComplete,
        );
      },
    );
  }
}
