import 'package:flutter/material.dart';
import 'package:vid_base_project/product/common_blocs/index.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/utils/extensions.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_in_app_review_manager/vid_in_app_review_manager.dart';

mixin OnboardingMixin {

  final ValueNotifier<bool> isAnalyzing = ValueNotifier(false);

  Future<void> onOpen(BuildContext context) async {
    final updatedUserFlow = await LocalDbManager.instance.updateUserFlow(
      context.sessionState.userFlow.copyWith(
        isFirstLaunch: false, 
      )
    );
    context.sessionEvent(SessionEvent.updateUserFlow(updatedUserFlow));
  }

  Future<void> requestReviewIfEligible() async =>
      VidInAppReviewManager.instance.requestReviewIfEligible();

  Future<void> askForAppTrackingTransparency() async => VidPermissionManager()
      .requestPermission(permission: Permission.appTrackingTransparency);

  Future<void> onComplete() async {
    isAnalyzing.value = true;
  }
}
