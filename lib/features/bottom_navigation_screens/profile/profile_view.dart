import 'package:flutter/material.dart';
import 'package:vid_base_project/config/config.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/profile/profile_mixin.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_base_project/product/utils/extensions.dart';
import 'package:vid_base_project/product/utils/index.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

class ProfileView extends StatelessWidget with ProfileMixin {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: VidMainHeader(
        title: VidLocalizationKeys.generalSettingsProfileTitle.tr(
          context: context,
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            FCard(
              title: Text(
                LocalizationKeys.premiumPremiumCtaTitle.tr(context: context),
              ),
              subtitle: Text(
                LocalizationKeys.premiumPremiumCtaDescription.tr(
                  context: context,
                ),
              ),
              child: Align(
                alignment: Alignment.centerRight,
                child: FButton(
                  intrinsicWidth: true,
                  onPress: () => context.navigateToPremium(),
                  prefix: const Icon(FIcons.rocket),
                  child: Text(
                    VidLocalizationKeys.actionsUpgradeNow.tr(context: context),
                  ),
                ),
              ),
            ),
            FTileGroup(
              label: Text(
                VidLocalizationKeys.generalSettingsSecuritySettingsTitle.tr(
                  context: context,
                ),
              ),
              children: [
                if (secureAppController.canCheckBiometrics)
                  FTile(
                    title: Text(
                      VidLocalizationKeys.generalSettingsBiometricsTitle.tr(
                        context: context,
                      ),
                    ),
                    suffixIcon: const Icon(Icons.arrow_forward_ios),
                    prefixIcon: const Icon(Icons.fingerprint_outlined),
                    details: ValueListenableBuilder(
                      valueListenable: secureAppController.securityModel,
                      builder: (context, value, child) => Text(
                        onOffText(context, value: value.isFaceIdEnabled),
                      ),
                    ),
                    onPress: () => toggleFaceId(context),
                  ),
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsPinLockTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.key_outlined),
                  details: ValueListenableBuilder(
                    valueListenable: secureAppController.securityModel,
                    builder: (context, value, child) =>
                        Text(onOffText(context, value: value.isPinEnabled)),
                  ),
                  onPress: () => togglePin(context),
                ),
                FTile(
                  title: Text(
                    VidLocalizationKeys.advancedSecurityTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.shield_outlined),
                  onPress: () => onTapAdvancedSecurity(context),
                ),
                FTile(
                  key: const ValueKey('security-checks'),
                  title: Text(
                    VidLocalizationKeys.generalSettingsSecurityChecksTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.local_police_outlined),
                  onPress: () => onTapSecurityCheck(context),
                ),
              ],
            ),
            FTileGroup(
              label: Text(
                VidLocalizationKeys.generalSettingsAppearanceLanguageTitle.tr(
                  context: context,
                ),
              ),
              children: [
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsThemeTitle.tr(
                      context: context,
                    ),
                  ),
                  details: ValueListenableBuilder(
                    valueListenable: appNotifier.appUtil,
                    builder: (context, value, child) =>
                        Text(themeCodeTranslation(value.themeMode)),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.color_lens_outlined),
                  onPress: () => onTapSelectTheme(context),
                ),
                FTile(
                  key: const ValueKey('language'),
                  title: Text(
                    VidLocalizationKeys.generalSettingsLanguageTitle.tr(
                      context: context,
                    ),
                  ),
                  details: Text(localeTranslation(context.locale)),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.language_outlined),
                  onPress: () => onTapSelectLanguage(context),
                ),
              ],
            ),
            FTileGroup(
              label: Text(
                VidLocalizationKeys.generalSettingsFeedbackSharingTitle.tr(
                  context: context,
                ),
              ),
              children: [
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsRateUsTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.star_outline),
                  onPress: () => onTapRateApp(context),
                ),
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsShareUsTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.share_outlined),
                  onPress: () => onTapShareApp(context),
                ),
                FTile(
                  key: const ValueKey('our-apps'),
                  title: Text(
                    VidLocalizationKeys.ourAppsButtonTitle.tr(context: context),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.explore_outlined),
                  onPress: () => onTapOurApps(context),
                ),
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsRequestNewFeature.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.add_outlined),
                  onPress: () => onTapRequestNewFeature(context),
                ),
              ],
            ),
            FTileGroup(
              label: Text(
                VidLocalizationKeys.generalSettingsSupportLegalTitle.tr(
                  context: context,
                ),
              ),
              children: [
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsFaqsTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.question_mark_outlined),
                  onPress: () => onTapFaq(context),
                ),
                FTile(
                  key: const ValueKey('contact-us'),
                  title: Text(
                    VidLocalizationKeys.generalSettingsContactUsTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.email_outlined),
                  onPress: () => onTapContactUs(context),
                ),
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsPrivacyPolicyTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.lock_open_outlined),
                  onPress: () =>
                      onTapPrivacyPolicy(context, url: kPrivacyPolicyUrl),
                ),
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsTermsOfServiceTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.rule_outlined),
                  onPress: () =>
                      onTapPrivacyPolicy(context, url: kTermsOfServiceUrl),
                ),
              ],
            ),
            FTileGroup(
              label: Text(
                VidLocalizationKeys.generalSettingsAccountTitle.tr(
                  context: context,
                ),
              ),
              children: [
                FTile(
                  title: Text(
                    VidLocalizationKeys.generalSettingsClearLocalDataTitle.tr(
                      context: context,
                    ),
                  ),
                  suffixIcon: const Icon(Icons.arrow_forward_ios),
                  prefixIcon: const Icon(Icons.folder_delete_outlined),
                  onPress: () => onClearLocalData(context),
                ),
              ],
            ),
            kLargeSpacing.heightBox,
            Text(VidAppInfo().versionToDisplay, style: context.textSM),
            kMediumSpacing.heightBox,
          ],
        ),
      ),
    );
  }
}
