import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vid_base_project/config/config.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_in_app_review_manager/vid_in_app_review_manager.dart';
import 'package:vid_ui/vid_ui.dart';

mixin ProfileMixin {
  Future<void> togglePin(BuildContext context) async {
    if (secureAppController.securityModel.value.isPinEnabled) {
      final res = await showVidActionSheet<bool>(
        context,
        title: VidLocalizationKeys.generalSettingsPinLockTitle.tr(context: context),
        items: [
          (
            label: VidLocalizationKeys.pinLockRemovePin.tr(context: context),
            value: false,
          ),
          (
            label: VidLocalizationKeys.pinLockUpdatePin.tr(context: context),
            value: true,
          ),
        ],
      );
      if (res != null) {
        await secureAppController.updatePinLock(context, isRemove: !res);
      }
    } else {
      await secureAppController.updatePinLock(context);
    }
  }

  Future<void> toggleFaceId(BuildContext context) async {
    await secureAppController.toggleFaceId(context);
  }

  Future<void> onTapShareApp(BuildContext context) async {
    try {
      await VidShareManager.shareAppUrl(
        context,
        text: LocalizationKeys.shareText.trWithArgs([
          kAppName,
        ], context: context),
        subject: kAppName,
        appStoreUrl: VidLocalizationKeys.shareAppContentDownloadAppStore
            .trWithArgs([kAppStoreUrl], context: context),
      );
    } on Exception catch (_) {
      unawaited(showVidErrorDialog(context));
    }
  }

  Future<void> onTapRateApp(BuildContext context) async {
    try {
      await VidInAppReviewManager.instance.openStore(appStoreId: kAppStoreId);
    } on Exception catch (_) {
      await showVidErrorDialog(context);
    }
  }

  void onTapRequestNewFeature(BuildContext context) {
    context.pushNamed(
      OtherRoutePaths.contactUs.name,
      extra: VidContactUsSubject.featureRequest,
    );
  }

  void onTapExploreMoreApps(BuildContext context) {
    context.pushNamed(OtherRoutePaths.ourApps.name);
  }

  void onTapAdvancedSecurity(BuildContext context) {
    context.pushNamed(OtherRoutePaths.advancedSecurity.name);
  }

  void onTapSecurityCheck(BuildContext context) {
    context.pushNamed(OtherRoutePaths.securityCheck.name);
  }

  void onTapOurApps(BuildContext context) {
    context.pushNamed(OtherRoutePaths.ourApps.name);
  }

  void onTapFaq(BuildContext context) {
    context.pushNamed(OtherRoutePaths.faq.name);
  }

  void onTapContactUs(BuildContext context) {
    context.pushNamed(OtherRoutePaths.contactUs.name);
  }

  Future<void> onTapSelectTheme(BuildContext context) async {
    final res = await showVidThemePicker(
      context: context,
      theme: appNotifier.appUtil.value.theme,
      themeMode: appNotifier.appUtil.value.themeMode,
    );
    if (res != null) {
      unawaited(appNotifier.updateAppUtil(res));
    }
  }

  Future<void> onTapSelectLanguage(BuildContext context) async {
    final res = await showVidSingleSelectSheet<Locale>(
      context: context,
      items: supportedLocales,
      initialValue: context.locale,
      title: VidLocalizationKeys.generalSettingsLanguageTitle.tr(
        context: context,
      ),
      label: localeTranslation,
      description: VidLocalizationKeys.generalSettingsLanguageDescription.tr(
        context: context,
      ),
    );
    if (res != null) {
      unawaited(context.setLocale(res));
    }
  }

  Future<void> onTapPrivacyPolicy(
    BuildContext context, {
    required String url,
  }) async {
    try {
      await launchUrl(Uri.parse(url));
    } on Exception catch (_) {
      unawaited(showVidErrorDialog(context));
    }
  }

  Future<void> onClearLocalData(BuildContext context) async {
    final didConfirm = await showDeleteConfirmation(
      context,
      correctInput: kDeleteConfirmationInput,
      title: VidLocalizationKeys.generalSettingsClearLocalDataTitle.tr(
        context: context,
      ),
      description: VidLocalizationKeys.generalSettingsClearLocalDataWarning.tr(
        context: context,
      ),
    );

    if (didConfirm) {
      await LocalDbManager.instance.dropDb();
      context.goNamed(InitialRoutePaths.splash.name);
    }
  }
}
