import 'package:flutter/material.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/home/<USER>';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_core/vid_core.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> with HomeMixin {
  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: FHeader(
        title: Text(LocalizationKeys.bottomNavigationHome.tr(context: context)),
      ),
      child: const Column(),
    );
  }
}
