import 'package:flutter/material.dart';
import 'package:vid_ad_manager/vid_ad_manager.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/home/<USER>';
import 'package:vid_base_project/product/utils/extensions.dart';
import 'package:vid_base_project/product/utils/locator.dart';

mixin HomeMixin on State<HomeView> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final didSeePaywallBefore =
          context.sessionState.userFlow.isFirstPaywallSeen;
      final res = await context.navigateToPremium();
      if (!res) {
        await vidAdManager.initialize(
          adConfig,
          onInitialize: () {
            if (didSeePaywallBefore) {
              vidAdManager.maybeShowInterstitialAd();
            }
          },
        );
      }
    });
  }
}
