import 'package:flutter/material.dart';
import 'package:vid_ad_manager/vid_ad_manager.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';


List<({String name, IconData icon, int index})> destinations(
    BuildContext context,
  ) => [
    (
      name: LocalizationKeys.bottomNavigationHome.tr(context: context),
      icon: FIcons.house,
      index: 0,
    ),
    (
      name: LocalizationKeys.bottomNavigationHistory.tr(context: context),
      icon: FIcons.history,
      index: 1,
    ),
    (
      name: LocalizationKeys.bottomNavigationProfile.tr(context: context),
      icon: FIcons.user,
      index: 2,
    ),
  ];

class BottomNavigationBarWrapper extends StatelessWidget {
  const BottomNavigationBarWrapper({
    required this.child,
    required this.navigationShell,
    super.key,
  });

  final Widget child;
  final StatefulNavigationShell navigationShell;

  void _navigateToIndex(BuildContext context, {required int index}) {
    final destination = destinations(context)[index];
    if (destination.index != navigationShell.currentIndex) {
      navigationShell.goBranch(
        destination.index,
        initialLocation: destination.index == navigationShell.currentIndex,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return VidBottomNavigationBar(
      navigateToIndex: (index) => _navigateToIndex(context, index: index),
      destinations: destinations(context),
      currentIndex: navigationShell.currentIndex,
      child: child,

    );
  }
}

class VidBottomNavigationBar extends StatelessWidget {
  const VidBottomNavigationBar({
    required this.navigateToIndex,
    required this.destinations,
    required this.currentIndex,
    required this.child,
    super.key,
  });

  final void Function(int index) navigateToIndex;
  final int currentIndex;
  final Widget child;
  final List<({String name, IconData icon, int index})> destinations;

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      footer: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FBottomNavigationBar(
            index: currentIndex,
            onChange: navigateToIndex,
            children: destinations
                .map(
                  (destination) => FBottomNavigationBarItem(
                    key: ValueKey(destination.index),
                    icon: Icon(destination.icon),
                    label: Text(destination.name),
                  ),
                )
                .toList(),
          ),
        ],
      ),
      child: Column(
        children: [
          Expanded(child: child),
          vidAdManager.getBannerAdWidget(
            padding: const EdgeInsets.symmetric(vertical: kMediumSpacing),
          ),
        ],
      ),
    );
  }
}
