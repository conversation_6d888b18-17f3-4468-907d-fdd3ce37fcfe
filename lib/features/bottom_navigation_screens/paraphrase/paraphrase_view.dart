import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/cubit/paraphrase_cubit.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/cubit/paraphrase_state.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/paraphrase_mixin.dart';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

class ParaphraseView extends StatefulWidget {
  const ParaphraseView({super.key});

  @override
  State<ParaphraseView> createState() => _ParaphraseViewState();
}

class _ParaphraseViewState extends State<ParaphraseView> with ParaphraseMixin {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ParaphraseCubit(),
      child: FScaffold(
        header: FHeader(
          title: Text(LocalizationKeys.bottomNavigationParaphrase.tr(context: context)),
        ),
        child: BlocBuilder<ParaphraseCubit, ParaphraseState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Input Section
                  _buildInputSection(context, state),
                  
                  const SizedBox(height: 16),
                  
                  // Options Section
                  _buildOptionsSection(context, state),
                  
                  const SizedBox(height: 16),
                  
                  // Action Button
                  _buildActionButton(context, state),
                  
                  const SizedBox(height: 24),
                  
                  // Results Section
                  _buildResultsSection(context, state),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInputSection(BuildContext context, ParaphraseState state) {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationKeys.paraphraseInputTitle.tr(context: context),
              style: context.theme.typography.lg.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            FTextField(
              maxLines: 6,
              hint: LocalizationKeys.paraphraseInputHint.tr(context: context),
              onChange: context.read<ParaphraseCubit>().updateInputText,
              enabled: !state.isLoading,
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  LocalizationKeys.paraphraseWordCount.trWithArgs(
                    [state.inputWordCount.toString()],
                    context: context,
                  ),
                  style: context.theme.typography.sm.copyWith(
                    color: context.colorMutedForeground,
                  ),
                ),
                Text(
                  LocalizationKeys.paraphraseCharacterCount.trWithArgs(
                    [state.inputCharacterCount.toString()],
                    context: context,
                  ),
                  style: context.theme.typography.sm.copyWith(
                    color: context.colorMutedForeground,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsSection(BuildContext context, ParaphraseState state) {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationKeys.paraphraseOptionsTitle.tr(context: context),
              style: context.theme.typography.lg.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Style Selection
            _buildOptionRow(
              context,
              LocalizationKeys.paraphraseStyleLabel.tr(context: context),
              state.when(
                initial: (_, style, __, ___, ____, _____, ______, _______) => style.value,
                loading: (_, style, __, ___, ____, _____, ______, _______, ________) => style.value,
                success: (_, style, __, ___, ____, _____, ______, _______, ________, _________) => style.value,
                error: (_, style, __, ___, ____, _____, ______, _______, ________, _________) => style.value,
              ),
              onTap: () => showStylePicker(context),
            ),
            
            const SizedBox(height: 12),
            
            // Tone Selection
            _buildOptionRow(
              context,
              LocalizationKeys.paraphraseToneLabel.tr(context: context),
              state.when(
                initial: (_, __, tone, ___, ____, _____, ______, _______) => tone.value,
                loading: (_, __, tone, ___, ____, _____, ______, _______, ________) => tone.value,
                success: (_, __, tone, ___, ____, _____, ______, _______, ________, _________) => tone.value,
                error: (_, __, tone, ___, ____, _____, ______, _______, ________, _________) => tone.value,
              ),
              onTap: () => showTonePicker(context),
            ),
            
            const SizedBox(height: 12),
            
            // Length Selection
            _buildOptionRow(
              context,
              LocalizationKeys.paraphraseLengthLabel.tr(context: context),
              state.when(
                initial: (_, __, ___, length, ____, _____, ______, _______) => length.value,
                loading: (_, __, ___, length, ____, _____, ______, _______, ________) => length.value,
                success: (_, __, ___, length, ____, _____, ______, _______, ________, _________) => length.value,
                error: (_, __, ___, length, ____, _____, ______, _______, ________, _________) => length.value,
              ),
              onTap: () => showLengthPicker(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionRow(
    BuildContext context,
    String label,
    String value,
    {VoidCallback? onTap}
  ) {
    return FTile(
      title: Text(label),
      subtitle: Text(value.toUpperCase()),
      suffixIcon: const Icon(FIcons.chevronRight),
      onPress: onTap,
    );
  }

  Widget _buildActionButton(BuildContext context, ParaphraseState state) {
    return FButton(
      onPress: state.isInputValid && !state.isLoading
          ? () => context.read<ParaphraseCubit>().paraphrase()
          : null,
      prefix: state.isLoading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(FIcons.wand),
      child: Text(state.isLoading 
          ? LocalizationKeys.paraphraseProcessing.tr(context: context)
          : LocalizationKeys.paraphraseButton.tr(context: context)),
    );
  }

  Widget _buildResultsSection(BuildContext context, ParaphraseState state) {
    return state.when(
      initial: (_, __, ___, ____, _____, ______, _______, ________) => const SizedBox.shrink(),
      loading: (_, __, ___, ____, _____, ______, _______, ________, message) => 
          _buildLoadingCard(context, message),
      success: (_, __, ___, ____, _____, ______, response, _______, ________, _________) => 
          _buildSuccessCard(context, response),
      error: (_, __, ___, ____, _____, ______, errorMsg, _______, ________, errorCode) => 
          _buildErrorCard(context, errorMsg),
    );
  }

  Widget _buildLoadingCard(BuildContext context, String? message) {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              message ?? LocalizationKeys.paraphraseProcessing.tr(context: context),
              style: context.theme.typography.base,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessCard(BuildContext context, response) {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FIcons.checkCheck,
                  color: context.colorPrimary,
                ),
                const SizedBox(width: 8),
                Text(
                  LocalizationKeys.paraphraseResultsTitle.tr(context: context),
                  style: context.theme.typography.lg.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Display paraphrase options
            if (response.options.isNotEmpty)
              ...response.options.map((option) => 
                _buildOptionCard(context, option)
              ).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard(BuildContext context, option) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                option.text,
                style: context.theme.typography.base,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocalizationKeys.paraphraseConfidence.trWithArgs(
                      ['10'],
                      context: context,
                    ),
                    style: context.theme.typography.sm.copyWith(
                      color: context.colorMutedForeground,
                    ),
                  ),
                  Row(
                    children: [
                      FButton.icon(
                        child: Icon(FIcons.copy),
                        onPress: () => copyToClipboard(context, 'option.text'),
                      ),
                      const SizedBox(width: 8),
                      FButton.icon(
                        child:Icon(FIcons.share),
                        onPress: () => shareText(context, 'option.text'),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context, String errorMessage) {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              FIcons.badgeAlert,
              color: context.colorDestructive,
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              LocalizationKeys.paraphraseErrorTitle.tr(context: context),
              style: context.theme.typography.lg.copyWith(
                fontWeight: FontWeight.w600,
                color: context.colorDestructive,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: context.theme.typography.base,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            FButton(
              child: Text(LocalizationKeys.paraphraseTryAgain.tr(context: context)),
              onPress: () => context.read<ParaphraseCubit>().reset(),
            ),
          ],
        ),
      ),
    );
  }
}
