import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/cubit/paraphrase_cubit.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/models/index.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/paraphrase_view.dart';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

mixin ParaphraseMixin on State<ParaphraseView> {
  /// Show style picker bottom sheet
  Future<void> showStylePicker(BuildContext context) async {
    final cubit = context.read<ParaphraseCubit>();

    final selectedStyle = await showVidSingleSelectSheet<ParaphraseStyle>(
      context: context,
      title: LocalizationKeys.paraphraseStylePickerTitle.tr(context: context),
      items: ParaphraseStyle.values,
      label: (p0) => _getStyleDisplayName(context, p0),
    );

    if (selectedStyle != null) {
      cubit.updateStyle(selectedStyle);
    }
  }

  /// Show tone picker bottom sheet
  Future<void> showTonePicker(BuildContext context) async {
    final cubit = context.read<ParaphraseCubit>();

    final selectedTone = await showVidSingleSelectSheet<ParaphraseTone>(
      context: context,
      title: LocalizationKeys.paraphraseTonePickerTitle.tr(context: context),
      items: ParaphraseTone.values,
      label: (p0) => _getToneDisplayName(context, p0),
    );

    if (selectedTone != null) {
      cubit.updateTone(selectedTone);
    }
  }

  /// Show length picker bottom sheet
  Future<void> showLengthPicker(BuildContext context) async {
    final cubit = context.read<ParaphraseCubit>();

    final selectedLength = await showVidSingleSelectSheet<ParaphraseLength>(
      context: context,
      title: LocalizationKeys.paraphraseLengthPickerTitle.tr(context: context),
      items: ParaphraseLength.values,
      label: (p0) => _getLengthDisplayName(context, p0),
    );

    if (selectedLength != null) {
      cubit.updateLength(selectedLength);
    }
  }

  /// Copy text to clipboard
  Future<void> copyToClipboard(BuildContext context, String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));

      if (mounted) {
        unawaited(
          showVidSuccessToast(
            context,
            description: LocalizationKeys.paraphraseCopiedToClipboard.tr(
              context: context,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        unawaited(
          showVidErrorToast(
            context,
            description: LocalizationKeys.paraphraseCopyError.tr(
              context: context,
            ),
          ),
        );
      }
    }
  }

  /// Share text using platform share dialog
  Future<void> shareText(BuildContext context, String text) async {
    try {
      await VidShareManager.shareText(text);
    } catch (e) {
      if (mounted) {
        unawaited(
          showVidErrorToast(
            context,
            description: LocalizationKeys.paraphraseShareError.tr(
              context: context,
            ),
          ),
        );
      }
    }
  }

  /// Get display name for paraphrase style
  String _getStyleDisplayName(BuildContext context, ParaphraseStyle style) {
    switch (style) {
      case ParaphraseStyle.formal:
        return LocalizationKeys.paraphraseStyleFormal.tr(context: context);
      case ParaphraseStyle.casual:
        return LocalizationKeys.paraphraseStyleCasual.tr(context: context);
      case ParaphraseStyle.creative:
        return LocalizationKeys.paraphraseStyleCreative.tr(context: context);
      case ParaphraseStyle.simple:
        return LocalizationKeys.paraphraseStyleSimple.tr(context: context);
    }
  }

  /// Get display name for paraphrase tone
  String _getToneDisplayName(BuildContext context, ParaphraseTone tone) {
    switch (tone) {
      case ParaphraseTone.professional:
        return LocalizationKeys.paraphraseToneProfessional.tr(context: context);
      case ParaphraseTone.friendly:
        return LocalizationKeys.paraphraseToneFriendly.tr(context: context);
      case ParaphraseTone.academic:
        return LocalizationKeys.paraphraseToneAcademic.tr(context: context);
      case ParaphraseTone.neutral:
        return LocalizationKeys.paraphraseToneNeutral.tr(context: context);
    }
  }

  /// Get display name for paraphrase length
  String _getLengthDisplayName(BuildContext context, ParaphraseLength length) {
    switch (length) {
      case ParaphraseLength.shorter:
        return LocalizationKeys.paraphraseLengthShorter.tr(context: context);
      case ParaphraseLength.similar:
        return LocalizationKeys.paraphraseLengthSimilar.tr(context: context);
      case ParaphraseLength.longer:
        return LocalizationKeys.paraphraseLengthLonger.tr(context: context);
    }
  }

  /// Show confirmation dialog for clearing input
  Future<bool> showClearConfirmation(BuildContext context) async {
    final result = await showVidDialog(
      context,
      title: LocalizationKeys.paraphraseClearConfirmationTitle.tr(
        context: context,
      ),
      description: LocalizationKeys.paraphraseClearConfirmationMessage.tr(
        context: context,
      ),
      onPrimaryButtonPress: () => context.pop(true),
      primaryButtonText: LocalizationKeys.paraphraseClearConfirmationConfirm.tr(
        context: context,
      ),
      secondaryButtonText: LocalizationKeys.paraphraseClearConfirmationConfirm
          .tr(context: context),
      onSecondaryButtonPress: () => context.pop(false),
    );

    return false;
  }

  /// Paste text from clipboard
  Future<void> pasteFromClipboard(BuildContext context) async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;

      if (text != null && text.isNotEmpty) {
        context.read<ParaphraseCubit>().updateInputText(text);

        if (mounted) {
          unawaited(
            showVidSuccessToast(
              context,
              description: LocalizationKeys.paraphrasePastedFromClipboard.tr(
                context: context,
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          unawaited(
            showVidErrorToast(
              context,
              description: LocalizationKeys.paraphraseClipboardEmpty.tr(
                context: context,
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        unawaited(
          showVidErrorToast(
            context,
            description: LocalizationKeys.paraphrasePasteError.tr(
              context: context,
            ),
          ),
        );
      }
    }
  }

  /// Clear input text with confirmation
  Future<void> clearInputWithConfirmation(BuildContext context) async {
    final currentText = context.read<ParaphraseCubit>().state.inputText;

    if (currentText.trim().isEmpty) {
      return;
    }

    final shouldClear = await showClearConfirmation(context);
    if (shouldClear) {
      context.read<ParaphraseCubit>().clearInput();

      if (mounted) {
        unawaited(
          showVidErrorToast(
            context,
            description: LocalizationKeys.paraphraseInputCleared.tr(
              context: context,
            ),
          ),
        );
      }
    }
  }

  /// Show language picker for source or target language
  Future<void> showLanguagePicker(
    BuildContext context, {
    required bool isSourceLanguage,
  }) async {
    final cubit = context.read<ParaphraseCubit>();

    // For now, we'll use a simple list of common languages
    // This can be expanded later with a more comprehensive language list
    final languages = [
      ('en', LocalizationKeys.languageEnglish.tr(context: context)),
      ('es', LocalizationKeys.languageSpanish.tr(context: context)),
      ('fr', LocalizationKeys.languageFrench.tr(context: context)),
      ('de', LocalizationKeys.languageGerman.tr(context: context)),
      ('it', LocalizationKeys.languageItalian.tr(context: context)),
      ('pt', LocalizationKeys.languagePortuguese.tr(context: context)),
      ('ru', LocalizationKeys.languageRussian.tr(context: context)),
      ('ja', LocalizationKeys.languageJapanese.tr(context: context)),
      ('ko', LocalizationKeys.languageKorean.tr(context: context)),
      ('zh', LocalizationKeys.languageChinese.tr(context: context)),
    ];

    final selectedLanguage = await showVidSingleSelectSheet<String>(
      context: context,
      title: isSourceLanguage
          ? LocalizationKeys.paraphraseSourceLanguagePickerTitle.tr(
              context: context,
            )
          : LocalizationKeys.paraphraseTargetLanguagePickerTitle.tr(
              context: context,
            ),
      items: languages.map((e) => e.$1).toList(),
      label: (p0) => p0,
    );

    if (selectedLanguage != null) {
      if (isSourceLanguage) {
        cubit.updateSourceLanguage(selectedLanguage);
      } else {
        cubit.updateTargetLanguage(selectedLanguage);
      }
    }
  }
}
