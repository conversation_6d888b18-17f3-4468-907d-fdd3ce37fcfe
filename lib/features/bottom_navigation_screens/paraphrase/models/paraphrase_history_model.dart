import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/models/paraphrase_request_model.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/models/paraphrase_response_model.dart';

part 'paraphrase_history_model.freezed.dart';
part 'paraphrase_history_model.g.dart';

/// Model for storing paraphrase history locally
@freezed
class ParaphraseHistoryModel with _$ParaphraseHistoryModel {
  const factory ParaphraseHistoryModel({
    required String id,
    required ParaphraseRequestModel request,
    ParaphraseResponseModel? response,
    @Default(false) bool isFavorite,
    @Default(false) bool isArchived,
    DateTime? createdAt,
    DateTime? lastUpdatedAt,
    DateTime? deletedAt,
    String? userNotes,
    @Default([]) List<String> tags,
  }) = _ParaphraseHistoryModel;

  const ParaphraseHistoryModel._();

  factory ParaphraseHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$ParaphraseHistoryModelFromJson(json);

  /// Check if this history item is deleted (soft delete)
  bool get isDeleted => deletedAt != null;

  /// Check if this history item has a successful response
  bool get hasSuccessfulResponse => 
      response != null && response!.isSuccessful && response!.hasValidOptions;

  /// Get the original text from request
  String get originalText => request.originalText;

  /// Get the best paraphrased text from response
  String? get bestParaphrasedText => response?.bestOption?.text;

  /// Get the style used for paraphrasing
  ParaphraseStyle get style => request.style;

  /// Get the tone used for paraphrasing
  ParaphraseTone get tone => request.tone;

  /// Get the length preference used
  ParaphraseLength get length => request.length;

  /// Get word count comparison
  ({int original, int paraphrased}) get wordCountComparison {
    final originalCount = request.wordCount;
    final paraphrasedCount = response?.bestOption?.wordCount ?? 0;
    return (original: originalCount, paraphrased: paraphrasedCount);
  }

  /// Get character count comparison
  ({int original, int paraphrased}) get characterCountComparison {
    final originalCount = request.characterCount;
    final paraphrasedCount = response?.bestOption?.characterCount ?? 0;
    return (original: originalCount, paraphrased: paraphrasedCount);
  }

  /// Check if this item matches search query
  bool matchesSearch(String query) {
    if (query.trim().isEmpty) return true;
    
    final lowerQuery = query.toLowerCase();
    return originalText.toLowerCase().contains(lowerQuery) ||
           (bestParaphrasedText?.toLowerCase().contains(lowerQuery) ?? false) ||
           (userNotes?.toLowerCase().contains(lowerQuery) ?? false) ||
           tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
  }
}
