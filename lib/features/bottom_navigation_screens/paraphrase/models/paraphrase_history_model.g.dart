// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paraphrase_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ParaphraseHistoryModel _$ParaphraseHistoryModelFromJson(
  Map<String, dynamic> json,
) => _ParaphraseHistoryModel(
  id: json['id'] as String,
  request: ParaphraseRequestModel.fromJson(
    json['request'] as Map<String, dynamic>,
  ),
  response: json['response'] == null
      ? null
      : ParaphraseResponseModel.fromJson(
          json['response'] as Map<String, dynamic>,
        ),
  isFavorite: json['isFavorite'] as bool? ?? false,
  isArchived: json['isArchived'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  lastUpdatedAt: json['lastUpdatedAt'] == null
      ? null
      : DateTime.parse(json['lastUpdatedAt'] as String),
  deletedAt: json['deletedAt'] == null
      ? null
      : DateTime.parse(json['deletedAt'] as String),
  userNotes: json['userNotes'] as String?,
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
);

Map<String, dynamic> _$ParaphraseHistoryModelToJson(
  _ParaphraseHistoryModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'request': instance.request,
  'response': instance.response,
  'isFavorite': instance.isFavorite,
  'isArchived': instance.isArchived,
  'createdAt': instance.createdAt?.toIso8601String(),
  'lastUpdatedAt': instance.lastUpdatedAt?.toIso8601String(),
  'deletedAt': instance.deletedAt?.toIso8601String(),
  'userNotes': instance.userNotes,
  'tags': instance.tags,
};
