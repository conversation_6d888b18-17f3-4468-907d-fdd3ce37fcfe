// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paraphrase_history_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ParaphraseHistoryModel {

 String get id; ParaphraseRequestModel get request; ParaphraseResponseModel? get response; bool get isFavorite; bool get isArchived; DateTime? get createdAt; DateTime? get lastUpdatedAt; DateTime? get deletedAt; String? get userNotes; List<String> get tags;
/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ParaphraseHistoryModelCopyWith<ParaphraseHistoryModel> get copyWith => _$ParaphraseHistoryModelCopyWithImpl<ParaphraseHistoryModel>(this as ParaphraseHistoryModel, _$identity);

  /// Serializes this ParaphraseHistoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ParaphraseHistoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.request, request) || other.request == request)&&(identical(other.response, response) || other.response == response)&&(identical(other.isFavorite, isFavorite) || other.isFavorite == isFavorite)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastUpdatedAt, lastUpdatedAt) || other.lastUpdatedAt == lastUpdatedAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.userNotes, userNotes) || other.userNotes == userNotes)&&const DeepCollectionEquality().equals(other.tags, tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,request,response,isFavorite,isArchived,createdAt,lastUpdatedAt,deletedAt,userNotes,const DeepCollectionEquality().hash(tags));

@override
String toString() {
  return 'ParaphraseHistoryModel(id: $id, request: $request, response: $response, isFavorite: $isFavorite, isArchived: $isArchived, createdAt: $createdAt, lastUpdatedAt: $lastUpdatedAt, deletedAt: $deletedAt, userNotes: $userNotes, tags: $tags)';
}


}

/// @nodoc
abstract mixin class $ParaphraseHistoryModelCopyWith<$Res>  {
  factory $ParaphraseHistoryModelCopyWith(ParaphraseHistoryModel value, $Res Function(ParaphraseHistoryModel) _then) = _$ParaphraseHistoryModelCopyWithImpl;
@useResult
$Res call({
 String id, ParaphraseRequestModel request, ParaphraseResponseModel? response, bool isFavorite, bool isArchived, DateTime? createdAt, DateTime? lastUpdatedAt, DateTime? deletedAt, String? userNotes, List<String> tags
});


$ParaphraseRequestModelCopyWith<$Res> get request;$ParaphraseResponseModelCopyWith<$Res>? get response;

}
/// @nodoc
class _$ParaphraseHistoryModelCopyWithImpl<$Res>
    implements $ParaphraseHistoryModelCopyWith<$Res> {
  _$ParaphraseHistoryModelCopyWithImpl(this._self, this._then);

  final ParaphraseHistoryModel _self;
  final $Res Function(ParaphraseHistoryModel) _then;

/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? request = null,Object? response = freezed,Object? isFavorite = null,Object? isArchived = null,Object? createdAt = freezed,Object? lastUpdatedAt = freezed,Object? deletedAt = freezed,Object? userNotes = freezed,Object? tags = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,request: null == request ? _self.request : request // ignore: cast_nullable_to_non_nullable
as ParaphraseRequestModel,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as ParaphraseResponseModel?,isFavorite: null == isFavorite ? _self.isFavorite : isFavorite // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastUpdatedAt: freezed == lastUpdatedAt ? _self.lastUpdatedAt : lastUpdatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,userNotes: freezed == userNotes ? _self.userNotes : userNotes // ignore: cast_nullable_to_non_nullable
as String?,tags: null == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}
/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ParaphraseRequestModelCopyWith<$Res> get request {
  
  return $ParaphraseRequestModelCopyWith<$Res>(_self.request, (value) {
    return _then(_self.copyWith(request: value));
  });
}/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ParaphraseResponseModelCopyWith<$Res>? get response {
    if (_self.response == null) {
    return null;
  }

  return $ParaphraseResponseModelCopyWith<$Res>(_self.response!, (value) {
    return _then(_self.copyWith(response: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ParaphraseHistoryModel extends ParaphraseHistoryModel {
  const _ParaphraseHistoryModel({required this.id, required this.request, this.response, this.isFavorite = false, this.isArchived = false, this.createdAt, this.lastUpdatedAt, this.deletedAt, this.userNotes, final  List<String> tags = const []}): _tags = tags,super._();
  factory _ParaphraseHistoryModel.fromJson(Map<String, dynamic> json) => _$ParaphraseHistoryModelFromJson(json);

@override final  String id;
@override final  ParaphraseRequestModel request;
@override final  ParaphraseResponseModel? response;
@override@JsonKey() final  bool isFavorite;
@override@JsonKey() final  bool isArchived;
@override final  DateTime? createdAt;
@override final  DateTime? lastUpdatedAt;
@override final  DateTime? deletedAt;
@override final  String? userNotes;
 final  List<String> _tags;
@override@JsonKey() List<String> get tags {
  if (_tags is EqualUnmodifiableListView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tags);
}


/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ParaphraseHistoryModelCopyWith<_ParaphraseHistoryModel> get copyWith => __$ParaphraseHistoryModelCopyWithImpl<_ParaphraseHistoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ParaphraseHistoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ParaphraseHistoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.request, request) || other.request == request)&&(identical(other.response, response) || other.response == response)&&(identical(other.isFavorite, isFavorite) || other.isFavorite == isFavorite)&&(identical(other.isArchived, isArchived) || other.isArchived == isArchived)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.lastUpdatedAt, lastUpdatedAt) || other.lastUpdatedAt == lastUpdatedAt)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.userNotes, userNotes) || other.userNotes == userNotes)&&const DeepCollectionEquality().equals(other._tags, _tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,request,response,isFavorite,isArchived,createdAt,lastUpdatedAt,deletedAt,userNotes,const DeepCollectionEquality().hash(_tags));

@override
String toString() {
  return 'ParaphraseHistoryModel(id: $id, request: $request, response: $response, isFavorite: $isFavorite, isArchived: $isArchived, createdAt: $createdAt, lastUpdatedAt: $lastUpdatedAt, deletedAt: $deletedAt, userNotes: $userNotes, tags: $tags)';
}


}

/// @nodoc
abstract mixin class _$ParaphraseHistoryModelCopyWith<$Res> implements $ParaphraseHistoryModelCopyWith<$Res> {
  factory _$ParaphraseHistoryModelCopyWith(_ParaphraseHistoryModel value, $Res Function(_ParaphraseHistoryModel) _then) = __$ParaphraseHistoryModelCopyWithImpl;
@override @useResult
$Res call({
 String id, ParaphraseRequestModel request, ParaphraseResponseModel? response, bool isFavorite, bool isArchived, DateTime? createdAt, DateTime? lastUpdatedAt, DateTime? deletedAt, String? userNotes, List<String> tags
});


@override $ParaphraseRequestModelCopyWith<$Res> get request;@override $ParaphraseResponseModelCopyWith<$Res>? get response;

}
/// @nodoc
class __$ParaphraseHistoryModelCopyWithImpl<$Res>
    implements _$ParaphraseHistoryModelCopyWith<$Res> {
  __$ParaphraseHistoryModelCopyWithImpl(this._self, this._then);

  final _ParaphraseHistoryModel _self;
  final $Res Function(_ParaphraseHistoryModel) _then;

/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? request = null,Object? response = freezed,Object? isFavorite = null,Object? isArchived = null,Object? createdAt = freezed,Object? lastUpdatedAt = freezed,Object? deletedAt = freezed,Object? userNotes = freezed,Object? tags = null,}) {
  return _then(_ParaphraseHistoryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,request: null == request ? _self.request : request // ignore: cast_nullable_to_non_nullable
as ParaphraseRequestModel,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as ParaphraseResponseModel?,isFavorite: null == isFavorite ? _self.isFavorite : isFavorite // ignore: cast_nullable_to_non_nullable
as bool,isArchived: null == isArchived ? _self.isArchived : isArchived // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastUpdatedAt: freezed == lastUpdatedAt ? _self.lastUpdatedAt : lastUpdatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,userNotes: freezed == userNotes ? _self.userNotes : userNotes // ignore: cast_nullable_to_non_nullable
as String?,tags: null == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ParaphraseRequestModelCopyWith<$Res> get request {
  
  return $ParaphraseRequestModelCopyWith<$Res>(_self.request, (value) {
    return _then(_self.copyWith(request: value));
  });
}/// Create a copy of ParaphraseHistoryModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ParaphraseResponseModelCopyWith<$Res>? get response {
    if (_self.response == null) {
    return null;
  }

  return $ParaphraseResponseModelCopyWith<$Res>(_self.response!, (value) {
    return _then(_self.copyWith(response: value));
  });
}
}

// dart format on
