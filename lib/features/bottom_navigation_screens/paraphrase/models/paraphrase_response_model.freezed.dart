// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paraphrase_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ParaphraseOption {

 String get id; String get text; double get confidence; String? get explanation;
/// Create a copy of ParaphraseOption
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ParaphraseOptionCopyWith<ParaphraseOption> get copyWith => _$ParaphraseOptionCopyWithImpl<ParaphraseOption>(this as ParaphraseOption, _$identity);

  /// Serializes this ParaphraseOption to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ParaphraseOption&&(identical(other.id, id) || other.id == id)&&(identical(other.text, text) || other.text == text)&&(identical(other.confidence, confidence) || other.confidence == confidence)&&(identical(other.explanation, explanation) || other.explanation == explanation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,text,confidence,explanation);

@override
String toString() {
  return 'ParaphraseOption(id: $id, text: $text, confidence: $confidence, explanation: $explanation)';
}


}

/// @nodoc
abstract mixin class $ParaphraseOptionCopyWith<$Res>  {
  factory $ParaphraseOptionCopyWith(ParaphraseOption value, $Res Function(ParaphraseOption) _then) = _$ParaphraseOptionCopyWithImpl;
@useResult
$Res call({
 String id, String text, double confidence, String? explanation
});




}
/// @nodoc
class _$ParaphraseOptionCopyWithImpl<$Res>
    implements $ParaphraseOptionCopyWith<$Res> {
  _$ParaphraseOptionCopyWithImpl(this._self, this._then);

  final ParaphraseOption _self;
  final $Res Function(ParaphraseOption) _then;

/// Create a copy of ParaphraseOption
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? text = null,Object? confidence = null,Object? explanation = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,explanation: freezed == explanation ? _self.explanation : explanation // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ParaphraseOption extends ParaphraseOption {
  const _ParaphraseOption({required this.id, required this.text, this.confidence = 0.0, this.explanation}): super._();
  factory _ParaphraseOption.fromJson(Map<String, dynamic> json) => _$ParaphraseOptionFromJson(json);

@override final  String id;
@override final  String text;
@override@JsonKey() final  double confidence;
@override final  String? explanation;

/// Create a copy of ParaphraseOption
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ParaphraseOptionCopyWith<_ParaphraseOption> get copyWith => __$ParaphraseOptionCopyWithImpl<_ParaphraseOption>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ParaphraseOptionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ParaphraseOption&&(identical(other.id, id) || other.id == id)&&(identical(other.text, text) || other.text == text)&&(identical(other.confidence, confidence) || other.confidence == confidence)&&(identical(other.explanation, explanation) || other.explanation == explanation));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,text,confidence,explanation);

@override
String toString() {
  return 'ParaphraseOption(id: $id, text: $text, confidence: $confidence, explanation: $explanation)';
}


}

/// @nodoc
abstract mixin class _$ParaphraseOptionCopyWith<$Res> implements $ParaphraseOptionCopyWith<$Res> {
  factory _$ParaphraseOptionCopyWith(_ParaphraseOption value, $Res Function(_ParaphraseOption) _then) = __$ParaphraseOptionCopyWithImpl;
@override @useResult
$Res call({
 String id, String text, double confidence, String? explanation
});




}
/// @nodoc
class __$ParaphraseOptionCopyWithImpl<$Res>
    implements _$ParaphraseOptionCopyWith<$Res> {
  __$ParaphraseOptionCopyWithImpl(this._self, this._then);

  final _ParaphraseOption _self;
  final $Res Function(_ParaphraseOption) _then;

/// Create a copy of ParaphraseOption
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? text = null,Object? confidence = null,Object? explanation = freezed,}) {
  return _then(_ParaphraseOption(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,confidence: null == confidence ? _self.confidence : confidence // ignore: cast_nullable_to_non_nullable
as double,explanation: freezed == explanation ? _self.explanation : explanation // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ParaphraseResponseModel {

 String get id; String get requestId; List<ParaphraseOption> get options; DateTime? get createdAt; bool get isSuccessful; String? get errorMessage; int get processingTimeMs;
/// Create a copy of ParaphraseResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ParaphraseResponseModelCopyWith<ParaphraseResponseModel> get copyWith => _$ParaphraseResponseModelCopyWithImpl<ParaphraseResponseModel>(this as ParaphraseResponseModel, _$identity);

  /// Serializes this ParaphraseResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ParaphraseResponseModel&&(identical(other.id, id) || other.id == id)&&(identical(other.requestId, requestId) || other.requestId == requestId)&&const DeepCollectionEquality().equals(other.options, options)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isSuccessful, isSuccessful) || other.isSuccessful == isSuccessful)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.processingTimeMs, processingTimeMs) || other.processingTimeMs == processingTimeMs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,requestId,const DeepCollectionEquality().hash(options),createdAt,isSuccessful,errorMessage,processingTimeMs);

@override
String toString() {
  return 'ParaphraseResponseModel(id: $id, requestId: $requestId, options: $options, createdAt: $createdAt, isSuccessful: $isSuccessful, errorMessage: $errorMessage, processingTimeMs: $processingTimeMs)';
}


}

/// @nodoc
abstract mixin class $ParaphraseResponseModelCopyWith<$Res>  {
  factory $ParaphraseResponseModelCopyWith(ParaphraseResponseModel value, $Res Function(ParaphraseResponseModel) _then) = _$ParaphraseResponseModelCopyWithImpl;
@useResult
$Res call({
 String id, String requestId, List<ParaphraseOption> options, DateTime? createdAt, bool isSuccessful, String? errorMessage, int processingTimeMs
});




}
/// @nodoc
class _$ParaphraseResponseModelCopyWithImpl<$Res>
    implements $ParaphraseResponseModelCopyWith<$Res> {
  _$ParaphraseResponseModelCopyWithImpl(this._self, this._then);

  final ParaphraseResponseModel _self;
  final $Res Function(ParaphraseResponseModel) _then;

/// Create a copy of ParaphraseResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? requestId = null,Object? options = null,Object? createdAt = freezed,Object? isSuccessful = null,Object? errorMessage = freezed,Object? processingTimeMs = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,requestId: null == requestId ? _self.requestId : requestId // ignore: cast_nullable_to_non_nullable
as String,options: null == options ? _self.options : options // ignore: cast_nullable_to_non_nullable
as List<ParaphraseOption>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isSuccessful: null == isSuccessful ? _self.isSuccessful : isSuccessful // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,processingTimeMs: null == processingTimeMs ? _self.processingTimeMs : processingTimeMs // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ParaphraseResponseModel extends ParaphraseResponseModel {
  const _ParaphraseResponseModel({required this.id, required this.requestId, required final  List<ParaphraseOption> options, this.createdAt, this.isSuccessful = false, this.errorMessage, this.processingTimeMs = 0}): _options = options,super._();
  factory _ParaphraseResponseModel.fromJson(Map<String, dynamic> json) => _$ParaphraseResponseModelFromJson(json);

@override final  String id;
@override final  String requestId;
 final  List<ParaphraseOption> _options;
@override List<ParaphraseOption> get options {
  if (_options is EqualUnmodifiableListView) return _options;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_options);
}

@override final  DateTime? createdAt;
@override@JsonKey() final  bool isSuccessful;
@override final  String? errorMessage;
@override@JsonKey() final  int processingTimeMs;

/// Create a copy of ParaphraseResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ParaphraseResponseModelCopyWith<_ParaphraseResponseModel> get copyWith => __$ParaphraseResponseModelCopyWithImpl<_ParaphraseResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ParaphraseResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ParaphraseResponseModel&&(identical(other.id, id) || other.id == id)&&(identical(other.requestId, requestId) || other.requestId == requestId)&&const DeepCollectionEquality().equals(other._options, _options)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isSuccessful, isSuccessful) || other.isSuccessful == isSuccessful)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.processingTimeMs, processingTimeMs) || other.processingTimeMs == processingTimeMs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,requestId,const DeepCollectionEquality().hash(_options),createdAt,isSuccessful,errorMessage,processingTimeMs);

@override
String toString() {
  return 'ParaphraseResponseModel(id: $id, requestId: $requestId, options: $options, createdAt: $createdAt, isSuccessful: $isSuccessful, errorMessage: $errorMessage, processingTimeMs: $processingTimeMs)';
}


}

/// @nodoc
abstract mixin class _$ParaphraseResponseModelCopyWith<$Res> implements $ParaphraseResponseModelCopyWith<$Res> {
  factory _$ParaphraseResponseModelCopyWith(_ParaphraseResponseModel value, $Res Function(_ParaphraseResponseModel) _then) = __$ParaphraseResponseModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String requestId, List<ParaphraseOption> options, DateTime? createdAt, bool isSuccessful, String? errorMessage, int processingTimeMs
});




}
/// @nodoc
class __$ParaphraseResponseModelCopyWithImpl<$Res>
    implements _$ParaphraseResponseModelCopyWith<$Res> {
  __$ParaphraseResponseModelCopyWithImpl(this._self, this._then);

  final _ParaphraseResponseModel _self;
  final $Res Function(_ParaphraseResponseModel) _then;

/// Create a copy of ParaphraseResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? requestId = null,Object? options = null,Object? createdAt = freezed,Object? isSuccessful = null,Object? errorMessage = freezed,Object? processingTimeMs = null,}) {
  return _then(_ParaphraseResponseModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,requestId: null == requestId ? _self.requestId : requestId // ignore: cast_nullable_to_non_nullable
as String,options: null == options ? _self._options : options // ignore: cast_nullable_to_non_nullable
as List<ParaphraseOption>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isSuccessful: null == isSuccessful ? _self.isSuccessful : isSuccessful // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,processingTimeMs: null == processingTimeMs ? _self.processingTimeMs : processingTimeMs // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
