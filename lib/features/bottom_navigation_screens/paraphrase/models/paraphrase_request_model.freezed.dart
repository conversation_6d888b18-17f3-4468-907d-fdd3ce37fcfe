// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paraphrase_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ParaphraseRequestModel {

 String get id; String get originalText; ParaphraseStyle get style; ParaphraseTone get tone; ParaphraseLength get length; String get sourceLanguage; String get targetLanguage; DateTime? get createdAt;
/// Create a copy of ParaphraseRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ParaphraseRequestModelCopyWith<ParaphraseRequestModel> get copyWith => _$ParaphraseRequestModelCopyWithImpl<ParaphraseRequestModel>(this as ParaphraseRequestModel, _$identity);

  /// Serializes this ParaphraseRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ParaphraseRequestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.originalText, originalText) || other.originalText == originalText)&&(identical(other.style, style) || other.style == style)&&(identical(other.tone, tone) || other.tone == tone)&&(identical(other.length, length) || other.length == length)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,originalText,style,tone,length,sourceLanguage,targetLanguage,createdAt);

@override
String toString() {
  return 'ParaphraseRequestModel(id: $id, originalText: $originalText, style: $style, tone: $tone, length: $length, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $ParaphraseRequestModelCopyWith<$Res>  {
  factory $ParaphraseRequestModelCopyWith(ParaphraseRequestModel value, $Res Function(ParaphraseRequestModel) _then) = _$ParaphraseRequestModelCopyWithImpl;
@useResult
$Res call({
 String id, String originalText, ParaphraseStyle style, ParaphraseTone tone, ParaphraseLength length, String sourceLanguage, String targetLanguage, DateTime? createdAt
});




}
/// @nodoc
class _$ParaphraseRequestModelCopyWithImpl<$Res>
    implements $ParaphraseRequestModelCopyWith<$Res> {
  _$ParaphraseRequestModelCopyWithImpl(this._self, this._then);

  final ParaphraseRequestModel _self;
  final $Res Function(ParaphraseRequestModel) _then;

/// Create a copy of ParaphraseRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? originalText = null,Object? style = null,Object? tone = null,Object? length = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? createdAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,originalText: null == originalText ? _self.originalText : originalText // ignore: cast_nullable_to_non_nullable
as String,style: null == style ? _self.style : style // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,tone: null == tone ? _self.tone : tone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ParaphraseRequestModel extends ParaphraseRequestModel {
  const _ParaphraseRequestModel({required this.id, required this.originalText, this.style = ParaphraseStyle.simple, this.tone = ParaphraseTone.neutral, this.length = ParaphraseLength.similar, this.sourceLanguage = 'en', this.targetLanguage = 'en', this.createdAt}): super._();
  factory _ParaphraseRequestModel.fromJson(Map<String, dynamic> json) => _$ParaphraseRequestModelFromJson(json);

@override final  String id;
@override final  String originalText;
@override@JsonKey() final  ParaphraseStyle style;
@override@JsonKey() final  ParaphraseTone tone;
@override@JsonKey() final  ParaphraseLength length;
@override@JsonKey() final  String sourceLanguage;
@override@JsonKey() final  String targetLanguage;
@override final  DateTime? createdAt;

/// Create a copy of ParaphraseRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ParaphraseRequestModelCopyWith<_ParaphraseRequestModel> get copyWith => __$ParaphraseRequestModelCopyWithImpl<_ParaphraseRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ParaphraseRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ParaphraseRequestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.originalText, originalText) || other.originalText == originalText)&&(identical(other.style, style) || other.style == style)&&(identical(other.tone, tone) || other.tone == tone)&&(identical(other.length, length) || other.length == length)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,originalText,style,tone,length,sourceLanguage,targetLanguage,createdAt);

@override
String toString() {
  return 'ParaphraseRequestModel(id: $id, originalText: $originalText, style: $style, tone: $tone, length: $length, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$ParaphraseRequestModelCopyWith<$Res> implements $ParaphraseRequestModelCopyWith<$Res> {
  factory _$ParaphraseRequestModelCopyWith(_ParaphraseRequestModel value, $Res Function(_ParaphraseRequestModel) _then) = __$ParaphraseRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String originalText, ParaphraseStyle style, ParaphraseTone tone, ParaphraseLength length, String sourceLanguage, String targetLanguage, DateTime? createdAt
});




}
/// @nodoc
class __$ParaphraseRequestModelCopyWithImpl<$Res>
    implements _$ParaphraseRequestModelCopyWith<$Res> {
  __$ParaphraseRequestModelCopyWithImpl(this._self, this._then);

  final _ParaphraseRequestModel _self;
  final $Res Function(_ParaphraseRequestModel) _then;

/// Create a copy of ParaphraseRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? originalText = null,Object? style = null,Object? tone = null,Object? length = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? createdAt = freezed,}) {
  return _then(_ParaphraseRequestModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,originalText: null == originalText ? _self.originalText : originalText // ignore: cast_nullable_to_non_nullable
as String,style: null == style ? _self.style : style // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,tone: null == tone ? _self.tone : tone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
