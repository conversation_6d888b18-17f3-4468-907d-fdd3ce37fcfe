// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paraphrase_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ParaphraseOption _$ParaphraseOptionFromJson(Map<String, dynamic> json) =>
    _ParaphraseOption(
      id: json['id'] as String,
      text: json['text'] as String,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.0,
      explanation: json['explanation'] as String?,
    );

Map<String, dynamic> _$ParaphraseOptionToJson(_ParaphraseOption instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'confidence': instance.confidence,
      'explanation': instance.explanation,
    };

_ParaphraseResponseModel _$ParaphraseResponseModelFromJson(
  Map<String, dynamic> json,
) => _ParaphraseResponseModel(
  id: json['id'] as String,
  requestId: json['requestId'] as String,
  options: (json['options'] as List<dynamic>)
      .map((e) => ParaphraseOption.fromJson(e as Map<String, dynamic>))
      .toList(),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  isSuccessful: json['isSuccessful'] as bool? ?? false,
  errorMessage: json['errorMessage'] as String?,
  processingTimeMs: (json['processingTimeMs'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$ParaphraseResponseModelToJson(
  _ParaphraseResponseModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'requestId': instance.requestId,
  'options': instance.options,
  'createdAt': instance.createdAt?.toIso8601String(),
  'isSuccessful': instance.isSuccessful,
  'errorMessage': instance.errorMessage,
  'processingTimeMs': instance.processingTimeMs,
};
