// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paraphrase_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ParaphraseRequestModel _$ParaphraseRequestModelFromJson(
  Map<String, dynamic> json,
) => _ParaphraseRequestModel(
  id: json['id'] as String,
  originalText: json['originalText'] as String,
  style:
      $enumDecodeNullable(_$ParaphraseStyleEnumMap, json['style']) ??
      ParaphraseStyle.simple,
  tone:
      $enumDecodeNullable(_$ParaphraseToneEnumMap, json['tone']) ??
      ParaphraseTone.neutral,
  length:
      $enumDecodeNullable(_$ParaphraseLengthEnumMap, json['length']) ??
      ParaphraseLength.similar,
  sourceLanguage: json['sourceLanguage'] as String? ?? 'en',
  targetLanguage: json['targetLanguage'] as String? ?? 'en',
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$ParaphraseRequestModelToJson(
  _ParaphraseRequestModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'originalText': instance.originalText,
  'style': _$ParaphraseStyleEnumMap[instance.style]!,
  'tone': _$ParaphraseToneEnumMap[instance.tone]!,
  'length': _$ParaphraseLengthEnumMap[instance.length]!,
  'sourceLanguage': instance.sourceLanguage,
  'targetLanguage': instance.targetLanguage,
  'createdAt': instance.createdAt?.toIso8601String(),
};

const _$ParaphraseStyleEnumMap = {
  ParaphraseStyle.formal: 'formal',
  ParaphraseStyle.casual: 'casual',
  ParaphraseStyle.creative: 'creative',
  ParaphraseStyle.simple: 'simple',
};

const _$ParaphraseToneEnumMap = {
  ParaphraseTone.professional: 'professional',
  ParaphraseTone.friendly: 'friendly',
  ParaphraseTone.academic: 'academic',
  ParaphraseTone.neutral: 'neutral',
};

const _$ParaphraseLengthEnumMap = {
  ParaphraseLength.shorter: 'shorter',
  ParaphraseLength.similar: 'similar',
  ParaphraseLength.longer: 'longer',
};
