import 'package:freezed_annotation/freezed_annotation.dart';

part 'paraphrase_response_model.freezed.dart';
part 'paraphrase_response_model.g.dart';

/// Model for individual paraphrase option
@freezed
class ParaphraseOption with _$ParaphraseOption {
  const factory ParaphraseOption({
    required String id,
    required String text,
    @Default(0.0) double confidence,
    String? explanation,
  }) = _ParaphraseOption;

  const ParaphraseOption._();

  factory ParaphraseOption.fromJson(Map<String, dynamic> json) =>
      _$ParaphraseOptionFromJson(json);

  /// Get word count of paraphrased text
  int get wordCount => text.trim().split(RegExp(r'\s+')).length;

  /// Get character count of paraphrased text
  int get characterCount => text.length;
}

/// Model for paraphrase response from AI service
@freezed
class ParaphraseResponseModel with _$ParaphraseResponseModel {
  const factory ParaphraseResponseModel({
    required String id,
    required String requestId,
    required List<ParaphraseOption> options,
    DateTime? createdAt,
    @Default(false) bool isSuccessful,
    String? errorMessage,
    @Default(0) int processingTimeMs,
  }) = _ParaphraseResponseModel;

  const ParaphraseResponseModel._();

  factory ParaphraseResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ParaphraseResponseModelFromJson(json);

  /// Get the best paraphrase option (highest confidence)
  ParaphraseOption? get bestOption {
    if (options.isEmpty) return null;
    return options.reduce((a, b) => a.confidence > b.confidence ? a : b);
  }

  /// Check if response has valid options
  bool get hasValidOptions => options.isNotEmpty && isSuccessful;

  /// Get total number of options
  int get optionCount => options.length;
}
