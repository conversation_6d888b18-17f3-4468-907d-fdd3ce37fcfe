import 'package:freezed_annotation/freezed_annotation.dart';

part 'paraphrase_request_model.freezed.dart';
part 'paraphrase_request_model.g.dart';

/// Enum for different paraphrase styles
enum ParaphraseStyle {
  formal('formal'),
  casual('casual'),
  creative('creative'),
  simple('simple');

  const ParaphraseStyle(this.value);
  final String value;
}

/// Enum for different tone adjustments
enum ParaphraseTone {
  professional('professional'),
  friendly('friendly'),
  academic('academic'),
  neutral('neutral');

  const ParaphraseTone(this.value);
  final String value;
}

/// Enum for length control
enum ParaphraseLength {
  shorter('shorter'),
  similar('similar'),
  longer('longer');

  const ParaphraseLength(this.value);
  final String value;
}

/// Model for paraphrase request data
@freezed
class ParaphraseRequestModel with _$ParaphraseRequestModel {
  const factory ParaphraseRequestModel({
    required String id,
    required String originalText,
    @Default(ParaphraseStyle.simple) ParaphraseStyle style,
    @Default(ParaphraseTone.neutral) ParaphraseTone tone,
    @Default(ParaphraseLength.similar) ParaphraseLength length,
    @Default('en') String sourceLanguage,
    @Default('en') String targetLanguage,
    DateTime? createdAt,
  }) = _ParaphraseRequestModel;

  const ParaphraseRequestModel._();

  factory ParaphraseRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ParaphraseRequestModelFromJson(json);

  /// Get word count of original text
  int get wordCount => originalText.trim().split(RegExp(r'\s+')).length;

  /// Get character count of original text
  int get characterCount => originalText.length;

  /// Check if request is valid
  bool get isValid => originalText.trim().isNotEmpty;
}
