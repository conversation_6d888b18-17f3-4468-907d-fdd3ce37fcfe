import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/cubit/paraphrase_state.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/models/index.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_core/vid_core.dart';

/// Cubit for managing paraphrase operations and state
class ParaphraseCubit extends Cubit<ParaphraseState> {
  ParaphraseCubit() : super(const ParaphraseState.initial()) {
    _loadHistoryFromDatabase();
  }

  /// Update input text
  void updateInputText(String text) {
    state.when(
      initial: (_, style, tone, length, sourceLang, targetLang, history, favorites) =>
          emit(ParaphraseState.initial(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
          )),
      loading: (_, style, tone, length, sourceLang, targetLang, history, favorites, __) =>
          emit(ParaphraseState.initial(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
          )),
      success: (_, style, tone, length, sourceLang, targetLang, __, history, favorites, ___) =>
          emit(ParaphraseState.initial(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
          )),
      error: (_, style, tone, length, sourceLang, targetLang, __, history, favorites, ___, ____) =>
          emit(ParaphraseState.initial(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
          )),
    );
  }

  /// Update paraphrase style
  void updateStyle(ParaphraseStyle style) {
    state.when(
      initial: (text, _, tone, length, sourceLang, targetLang, history, favorites) =>
          emit(ParaphraseState.initial(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
          )),
      loading: (text, _, tone, length, sourceLang, targetLang, history, favorites, message) =>
          emit(ParaphraseState.loading(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
            loadingMessage: message,
          )),
      success: (text, _, tone, length, sourceLang, targetLang, response, history, favorites, selectedId) =>
          emit(ParaphraseState.success(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            response: response,
            history: history,
            favorites: favorites,
            selectedOptionId: selectedId,
          )),
      error: (text, _, tone, length, sourceLang, targetLang, errorMsg, history, favorites, errorCode) =>
          emit(ParaphraseState.error(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            errorMessage: errorMsg,
            history: history,
            favorites: favorites,
            errorCode: errorCode,
          )),
    );
  }

  /// Update paraphrase tone
  void updateTone(ParaphraseTone tone) {
    // Similar implementation to updateStyle but for tone
    // Implementation follows same pattern as updateStyle
  }

  /// Update paraphrase length preference
  void updateLength(ParaphraseLength length) {
    // Similar implementation to updateStyle but for length
    // Implementation follows same pattern as updateStyle
  }

  /// Update source language
  void updateSourceLanguage(String language) {
    // Similar implementation to updateStyle but for source language
    // Implementation follows same pattern as updateStyle
  }

  /// Update target language
  void updateTargetLanguage(String language) {
    // Similar implementation to updateStyle but for target language
    // Implementation follows same pattern as updateStyle
  }

  /// Clear input text
  void clearInput() {
    updateInputText('');
  }

  /// Reset to initial state
  void reset() {
    emit(ParaphraseState.initial(
      history: state.currentHistory,
      favorites: state.currentFavorites,
    ));
  }

  /// Load history from local database
  Future<void> _loadHistoryFromDatabase() async {
    try {
      // This will be implemented when LocalDbManager is updated
      // final history = await LocalDbManager.instance.getParaphraseHistory();
      // final favorites = history.where((item) => item.isFavorite).toList();
      
      // For now, emit current state with empty lists
      emit(state.when(
        initial: (text, style, tone, length, sourceLang, targetLang, _, __) =>
            ParaphraseState.initial(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              history: [],
              favorites: [],
            ),
        loading: (text, style, tone, length, sourceLang, targetLang, _, __, message) =>
            ParaphraseState.loading(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              history: [],
              favorites: [],
              loadingMessage: message,
            ),
        success: (text, style, tone, length, sourceLang, targetLang, response, _, __, selectedId) =>
            ParaphraseState.success(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              response: response,
              history: [],
              favorites: [],
              selectedOptionId: selectedId,
            ),
        error: (text, style, tone, length, sourceLang, targetLang, errorMsg, _, __, errorCode) =>
            ParaphraseState.error(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              errorMessage: errorMsg,
              history: [],
              favorites: [],
              errorCode: errorCode,
            ),
      ));
    } catch (e) {
      // Handle error loading from database
      debugPrint('Error loading paraphrase history: $e');
    }
  }

  /// Perform paraphrasing operation
  Future<void> paraphrase() async {
    if (!state.isInputValid) {
      emit(ParaphraseState.error(
        inputText: state.currentInputText,
        selectedStyle: ParaphraseStyle.neutral,
        selectedTone: ParaphraseTone.neutral,
        selectedLength: ParaphraseLength.similar,
        sourceLanguage: 'en',
        targetLanguage: 'en',
        errorMessage: 'Input text cannot be empty',
        history: state.currentHistory,
        favorites: state.currentFavorites,
        errorCode: 'EMPTY_INPUT',
      ));
      return;
    }

    // Emit loading state
    emit(state.when(
      initial: (text, style, tone, length, sourceLang, targetLang, history, favorites) =>
          ParaphraseState.loading(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
            loadingMessage: 'Processing your text...',
          ),
      loading: (text, style, tone, length, sourceLang, targetLang, history, favorites, _) =>
          ParaphraseState.loading(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
            loadingMessage: 'Processing your text...',
          ),
      success: (text, style, tone, length, sourceLang, targetLang, _, history, favorites, __) =>
          ParaphraseState.loading(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
            loadingMessage: 'Processing your text...',
          ),
      error: (text, style, tone, length, sourceLang, targetLang, _, history, favorites, __, ___) =>
          ParaphraseState.loading(
            inputText: text,
            selectedStyle: style,
            selectedTone: tone,
            selectedLength: length,
            sourceLanguage: sourceLang,
            targetLanguage: targetLang,
            history: history,
            favorites: favorites,
            loadingMessage: 'Processing your text...',
          ),
    ));

    try {
      // Create request model
      final request = ParaphraseRequestModel(
        id: generateUUIDv4(),
        originalText: state.currentInputText,
        style: state.when(
          initial: (_, style, __, ___, ____, _____, ______, _______) => style,
          loading: (_, style, __, ___, ____, _____, ______, _______, ________) => style,
          success: (_, style, __, ___, ____, _____, ______, _______, ________, _________) => style,
          error: (_, style, __, ___, ____, _____, ______, _______, ________, _________) => style,
        ),
        tone: state.when(
          initial: (_, __, tone, ___, ____, _____, ______, _______) => tone,
          loading: (_, __, tone, ___, ____, _____, ______, _______, ________) => tone,
          success: (_, __, tone, ___, ____, _____, ______, _______, ________, _________) => tone,
          error: (_, __, tone, ___, ____, _____, ______, _______, ________, _________) => tone,
        ),
        length: state.when(
          initial: (_, __, ___, length, ____, _____, ______, _______) => length,
          loading: (_, __, ___, length, ____, _____, ______, _______, ________) => length,
          success: (_, __, ___, length, ____, _____, ______, _______, ________, _________) => length,
          error: (_, __, ___, length, ____, _____, ______, _______, ________, _________) => length,
        ),
        sourceLanguage: state.when(
          initial: (_, __, ___, ____, sourceLang, _____, ______, _______) => sourceLang,
          loading: (_, __, ___, ____, sourceLang, _____, ______, _______, ________) => sourceLang,
          success: (_, __, ___, ____, sourceLang, _____, ______, _______, ________, _________) => sourceLang,
          error: (_, __, ___, ____, sourceLang, _____, ______, _______, ________, _________) => sourceLang,
        ),
        targetLanguage: state.when(
          initial: (_, __, ___, ____, _____, targetLang, ______, _______) => targetLang,
          loading: (_, __, ___, ____, _____, targetLang, ______, _______, ________) => targetLang,
          success: (_, __, ___, ____, _____, targetLang, ______, _______, ________, _________) => targetLang,
          error: (_, __, ___, ____, _____, targetLang, ______, _______, ________, _________) => targetLang,
        ),
        createdAt: DateTime.now(),
      );

      // TODO: Implement AI service call here
      // This will be implemented in the next task
      // For now, create a mock response
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      final mockResponse = ParaphraseResponseModel(
        id: generateUUIDv4(),
        requestId: request.id,
        options: [
          ParaphraseOption(
            id: generateUUIDv4(),
            text: 'This is a mock paraphrased version of your text.',
            confidence: 0.95,
            explanation: 'Mock explanation for the paraphrase.',
          ),
        ],
        createdAt: DateTime.now(),
        isSuccessful: true,
        processingTimeMs: 2000,
      );

      // Emit success state
      emit(ParaphraseState.success(
        inputText: state.currentInputText,
        selectedStyle: request.style,
        selectedTone: request.tone,
        selectedLength: request.length,
        sourceLanguage: request.sourceLanguage,
        targetLanguage: request.targetLanguage,
        response: mockResponse,
        history: state.currentHistory,
        favorites: state.currentFavorites,
      ));

      // Save to history
      await _saveToHistory(request, mockResponse);

    } catch (e) {
      // Emit error state
      emit(ParaphraseState.error(
        inputText: state.currentInputText,
        selectedStyle: ParaphraseStyle.neutral,
        selectedTone: ParaphraseTone.neutral,
        selectedLength: ParaphraseLength.similar,
        sourceLanguage: 'en',
        targetLanguage: 'en',
        errorMessage: 'Failed to paraphrase text: ${e.toString()}',
        history: state.currentHistory,
        favorites: state.currentFavorites,
        errorCode: 'PARAPHRASE_ERROR',
      ));
    }
  }

  /// Save paraphrase result to history
  Future<void> _saveToHistory(
    ParaphraseRequestModel request,
    ParaphraseResponseModel response,
  ) async {
    try {
      final historyItem = ParaphraseHistoryModel(
        id: generateUUIDv4(),
        request: request,
        response: response,
        createdAt: DateTime.now(),
        lastUpdatedAt: DateTime.now(),
      );

      // TODO: Save to local database
      // await LocalDbManager.instance.addParaphraseHistory(historyItem);

      // For now, just add to current state
      final updatedHistory = [...state.currentHistory, historyItem];
      
      // Update state with new history
      emit(state.when(
        initial: (text, style, tone, length, sourceLang, targetLang, _, favorites) =>
            ParaphraseState.initial(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              history: updatedHistory,
              favorites: favorites,
            ),
        loading: (text, style, tone, length, sourceLang, targetLang, _, favorites, message) =>
            ParaphraseState.loading(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              history: updatedHistory,
              favorites: favorites,
              loadingMessage: message,
            ),
        success: (text, style, tone, length, sourceLang, targetLang, response, _, favorites, selectedId) =>
            ParaphraseState.success(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              response: response,
              history: updatedHistory,
              favorites: favorites,
              selectedOptionId: selectedId,
            ),
        error: (text, style, tone, length, sourceLang, targetLang, errorMsg, _, favorites, errorCode) =>
            ParaphraseState.error(
              inputText: text,
              selectedStyle: style,
              selectedTone: tone,
              selectedLength: length,
              sourceLanguage: sourceLang,
              targetLanguage: targetLang,
              errorMessage: errorMsg,
              history: updatedHistory,
              favorites: favorites,
              errorCode: errorCode,
            ),
      ));
    } catch (e) {
      debugPrint('Error saving to history: $e');
    }
  }
}
