// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paraphrase_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ParaphraseState {

 String get inputText; ParaphraseStyle get selectedStyle; ParaphraseTone get selectedTone; ParaphraseLength get selectedLength; String get sourceLanguage; String get targetLanguage; List<ParaphraseHistoryModel> get history; List<ParaphraseHistoryModel> get favorites;
/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ParaphraseStateCopyWith<ParaphraseState> get copyWith => _$ParaphraseStateCopyWithImpl<ParaphraseState>(this as ParaphraseState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ParaphraseState&&(identical(other.inputText, inputText) || other.inputText == inputText)&&(identical(other.selectedStyle, selectedStyle) || other.selectedStyle == selectedStyle)&&(identical(other.selectedTone, selectedTone) || other.selectedTone == selectedTone)&&(identical(other.selectedLength, selectedLength) || other.selectedLength == selectedLength)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&const DeepCollectionEquality().equals(other.history, history)&&const DeepCollectionEquality().equals(other.favorites, favorites));
}


@override
int get hashCode => Object.hash(runtimeType,inputText,selectedStyle,selectedTone,selectedLength,sourceLanguage,targetLanguage,const DeepCollectionEquality().hash(history),const DeepCollectionEquality().hash(favorites));

@override
String toString() {
  return 'ParaphraseState(inputText: $inputText, selectedStyle: $selectedStyle, selectedTone: $selectedTone, selectedLength: $selectedLength, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, history: $history, favorites: $favorites)';
}


}

/// @nodoc
abstract mixin class $ParaphraseStateCopyWith<$Res>  {
  factory $ParaphraseStateCopyWith(ParaphraseState value, $Res Function(ParaphraseState) _then) = _$ParaphraseStateCopyWithImpl;
@useResult
$Res call({
 String inputText, ParaphraseStyle selectedStyle, ParaphraseTone selectedTone, ParaphraseLength selectedLength, String sourceLanguage, String targetLanguage, List<ParaphraseHistoryModel> history, List<ParaphraseHistoryModel> favorites
});




}
/// @nodoc
class _$ParaphraseStateCopyWithImpl<$Res>
    implements $ParaphraseStateCopyWith<$Res> {
  _$ParaphraseStateCopyWithImpl(this._self, this._then);

  final ParaphraseState _self;
  final $Res Function(ParaphraseState) _then;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? inputText = null,Object? selectedStyle = null,Object? selectedTone = null,Object? selectedLength = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? history = null,Object? favorites = null,}) {
  return _then(_self.copyWith(
inputText: null == inputText ? _self.inputText : inputText // ignore: cast_nullable_to_non_nullable
as String,selectedStyle: null == selectedStyle ? _self.selectedStyle : selectedStyle // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,selectedTone: null == selectedTone ? _self.selectedTone : selectedTone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,selectedLength: null == selectedLength ? _self.selectedLength : selectedLength // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,history: null == history ? _self.history : history // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,favorites: null == favorites ? _self.favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,
  ));
}

}


/// @nodoc


class _Initial implements ParaphraseState {
  const _Initial({this.inputText = '', this.selectedStyle = ParaphraseStyle.simple, this.selectedTone = ParaphraseTone.neutral, this.selectedLength = ParaphraseLength.similar, this.sourceLanguage = 'en', this.targetLanguage = 'en', final  List<ParaphraseHistoryModel> history = const [], final  List<ParaphraseHistoryModel> favorites = const []}): _history = history,_favorites = favorites;
  

@override@JsonKey() final  String inputText;
@override@JsonKey() final  ParaphraseStyle selectedStyle;
@override@JsonKey() final  ParaphraseTone selectedTone;
@override@JsonKey() final  ParaphraseLength selectedLength;
@override@JsonKey() final  String sourceLanguage;
@override@JsonKey() final  String targetLanguage;
 final  List<ParaphraseHistoryModel> _history;
@override@JsonKey() List<ParaphraseHistoryModel> get history {
  if (_history is EqualUnmodifiableListView) return _history;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_history);
}

 final  List<ParaphraseHistoryModel> _favorites;
@override@JsonKey() List<ParaphraseHistoryModel> get favorites {
  if (_favorites is EqualUnmodifiableListView) return _favorites;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_favorites);
}


/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InitialCopyWith<_Initial> get copyWith => __$InitialCopyWithImpl<_Initial>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Initial&&(identical(other.inputText, inputText) || other.inputText == inputText)&&(identical(other.selectedStyle, selectedStyle) || other.selectedStyle == selectedStyle)&&(identical(other.selectedTone, selectedTone) || other.selectedTone == selectedTone)&&(identical(other.selectedLength, selectedLength) || other.selectedLength == selectedLength)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&const DeepCollectionEquality().equals(other._history, _history)&&const DeepCollectionEquality().equals(other._favorites, _favorites));
}


@override
int get hashCode => Object.hash(runtimeType,inputText,selectedStyle,selectedTone,selectedLength,sourceLanguage,targetLanguage,const DeepCollectionEquality().hash(_history),const DeepCollectionEquality().hash(_favorites));

@override
String toString() {
  return 'ParaphraseState.initial(inputText: $inputText, selectedStyle: $selectedStyle, selectedTone: $selectedTone, selectedLength: $selectedLength, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, history: $history, favorites: $favorites)';
}


}

/// @nodoc
abstract mixin class _$InitialCopyWith<$Res> implements $ParaphraseStateCopyWith<$Res> {
  factory _$InitialCopyWith(_Initial value, $Res Function(_Initial) _then) = __$InitialCopyWithImpl;
@override @useResult
$Res call({
 String inputText, ParaphraseStyle selectedStyle, ParaphraseTone selectedTone, ParaphraseLength selectedLength, String sourceLanguage, String targetLanguage, List<ParaphraseHistoryModel> history, List<ParaphraseHistoryModel> favorites
});




}
/// @nodoc
class __$InitialCopyWithImpl<$Res>
    implements _$InitialCopyWith<$Res> {
  __$InitialCopyWithImpl(this._self, this._then);

  final _Initial _self;
  final $Res Function(_Initial) _then;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? inputText = null,Object? selectedStyle = null,Object? selectedTone = null,Object? selectedLength = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? history = null,Object? favorites = null,}) {
  return _then(_Initial(
inputText: null == inputText ? _self.inputText : inputText // ignore: cast_nullable_to_non_nullable
as String,selectedStyle: null == selectedStyle ? _self.selectedStyle : selectedStyle // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,selectedTone: null == selectedTone ? _self.selectedTone : selectedTone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,selectedLength: null == selectedLength ? _self.selectedLength : selectedLength // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,history: null == history ? _self._history : history // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,favorites: null == favorites ? _self._favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,
  ));
}


}

/// @nodoc


class _Loading implements ParaphraseState {
  const _Loading({required this.inputText, required this.selectedStyle, required this.selectedTone, required this.selectedLength, required this.sourceLanguage, required this.targetLanguage, final  List<ParaphraseHistoryModel> history = const [], final  List<ParaphraseHistoryModel> favorites = const [], this.loadingMessage}): _history = history,_favorites = favorites;
  

@override final  String inputText;
@override final  ParaphraseStyle selectedStyle;
@override final  ParaphraseTone selectedTone;
@override final  ParaphraseLength selectedLength;
@override final  String sourceLanguage;
@override final  String targetLanguage;
 final  List<ParaphraseHistoryModel> _history;
@override@JsonKey() List<ParaphraseHistoryModel> get history {
  if (_history is EqualUnmodifiableListView) return _history;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_history);
}

 final  List<ParaphraseHistoryModel> _favorites;
@override@JsonKey() List<ParaphraseHistoryModel> get favorites {
  if (_favorites is EqualUnmodifiableListView) return _favorites;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_favorites);
}

 final  String? loadingMessage;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadingCopyWith<_Loading> get copyWith => __$LoadingCopyWithImpl<_Loading>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Loading&&(identical(other.inputText, inputText) || other.inputText == inputText)&&(identical(other.selectedStyle, selectedStyle) || other.selectedStyle == selectedStyle)&&(identical(other.selectedTone, selectedTone) || other.selectedTone == selectedTone)&&(identical(other.selectedLength, selectedLength) || other.selectedLength == selectedLength)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&const DeepCollectionEquality().equals(other._history, _history)&&const DeepCollectionEquality().equals(other._favorites, _favorites)&&(identical(other.loadingMessage, loadingMessage) || other.loadingMessage == loadingMessage));
}


@override
int get hashCode => Object.hash(runtimeType,inputText,selectedStyle,selectedTone,selectedLength,sourceLanguage,targetLanguage,const DeepCollectionEquality().hash(_history),const DeepCollectionEquality().hash(_favorites),loadingMessage);

@override
String toString() {
  return 'ParaphraseState.loading(inputText: $inputText, selectedStyle: $selectedStyle, selectedTone: $selectedTone, selectedLength: $selectedLength, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, history: $history, favorites: $favorites, loadingMessage: $loadingMessage)';
}


}

/// @nodoc
abstract mixin class _$LoadingCopyWith<$Res> implements $ParaphraseStateCopyWith<$Res> {
  factory _$LoadingCopyWith(_Loading value, $Res Function(_Loading) _then) = __$LoadingCopyWithImpl;
@override @useResult
$Res call({
 String inputText, ParaphraseStyle selectedStyle, ParaphraseTone selectedTone, ParaphraseLength selectedLength, String sourceLanguage, String targetLanguage, List<ParaphraseHistoryModel> history, List<ParaphraseHistoryModel> favorites, String? loadingMessage
});




}
/// @nodoc
class __$LoadingCopyWithImpl<$Res>
    implements _$LoadingCopyWith<$Res> {
  __$LoadingCopyWithImpl(this._self, this._then);

  final _Loading _self;
  final $Res Function(_Loading) _then;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? inputText = null,Object? selectedStyle = null,Object? selectedTone = null,Object? selectedLength = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? history = null,Object? favorites = null,Object? loadingMessage = freezed,}) {
  return _then(_Loading(
inputText: null == inputText ? _self.inputText : inputText // ignore: cast_nullable_to_non_nullable
as String,selectedStyle: null == selectedStyle ? _self.selectedStyle : selectedStyle // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,selectedTone: null == selectedTone ? _self.selectedTone : selectedTone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,selectedLength: null == selectedLength ? _self.selectedLength : selectedLength // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,history: null == history ? _self._history : history // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,favorites: null == favorites ? _self._favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,loadingMessage: freezed == loadingMessage ? _self.loadingMessage : loadingMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _Success implements ParaphraseState {
  const _Success({required this.inputText, required this.selectedStyle, required this.selectedTone, required this.selectedLength, required this.sourceLanguage, required this.targetLanguage, required this.response, final  List<ParaphraseHistoryModel> history = const [], final  List<ParaphraseHistoryModel> favorites = const [], this.selectedOptionId}): _history = history,_favorites = favorites;
  

@override final  String inputText;
@override final  ParaphraseStyle selectedStyle;
@override final  ParaphraseTone selectedTone;
@override final  ParaphraseLength selectedLength;
@override final  String sourceLanguage;
@override final  String targetLanguage;
 final  ParaphraseResponseModel response;
 final  List<ParaphraseHistoryModel> _history;
@override@JsonKey() List<ParaphraseHistoryModel> get history {
  if (_history is EqualUnmodifiableListView) return _history;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_history);
}

 final  List<ParaphraseHistoryModel> _favorites;
@override@JsonKey() List<ParaphraseHistoryModel> get favorites {
  if (_favorites is EqualUnmodifiableListView) return _favorites;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_favorites);
}

 final  String? selectedOptionId;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SuccessCopyWith<_Success> get copyWith => __$SuccessCopyWithImpl<_Success>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Success&&(identical(other.inputText, inputText) || other.inputText == inputText)&&(identical(other.selectedStyle, selectedStyle) || other.selectedStyle == selectedStyle)&&(identical(other.selectedTone, selectedTone) || other.selectedTone == selectedTone)&&(identical(other.selectedLength, selectedLength) || other.selectedLength == selectedLength)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&(identical(other.response, response) || other.response == response)&&const DeepCollectionEquality().equals(other._history, _history)&&const DeepCollectionEquality().equals(other._favorites, _favorites)&&(identical(other.selectedOptionId, selectedOptionId) || other.selectedOptionId == selectedOptionId));
}


@override
int get hashCode => Object.hash(runtimeType,inputText,selectedStyle,selectedTone,selectedLength,sourceLanguage,targetLanguage,response,const DeepCollectionEquality().hash(_history),const DeepCollectionEquality().hash(_favorites),selectedOptionId);

@override
String toString() {
  return 'ParaphraseState.success(inputText: $inputText, selectedStyle: $selectedStyle, selectedTone: $selectedTone, selectedLength: $selectedLength, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, response: $response, history: $history, favorites: $favorites, selectedOptionId: $selectedOptionId)';
}


}

/// @nodoc
abstract mixin class _$SuccessCopyWith<$Res> implements $ParaphraseStateCopyWith<$Res> {
  factory _$SuccessCopyWith(_Success value, $Res Function(_Success) _then) = __$SuccessCopyWithImpl;
@override @useResult
$Res call({
 String inputText, ParaphraseStyle selectedStyle, ParaphraseTone selectedTone, ParaphraseLength selectedLength, String sourceLanguage, String targetLanguage, ParaphraseResponseModel response, List<ParaphraseHistoryModel> history, List<ParaphraseHistoryModel> favorites, String? selectedOptionId
});


$ParaphraseResponseModelCopyWith<$Res> get response;

}
/// @nodoc
class __$SuccessCopyWithImpl<$Res>
    implements _$SuccessCopyWith<$Res> {
  __$SuccessCopyWithImpl(this._self, this._then);

  final _Success _self;
  final $Res Function(_Success) _then;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? inputText = null,Object? selectedStyle = null,Object? selectedTone = null,Object? selectedLength = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? response = null,Object? history = null,Object? favorites = null,Object? selectedOptionId = freezed,}) {
  return _then(_Success(
inputText: null == inputText ? _self.inputText : inputText // ignore: cast_nullable_to_non_nullable
as String,selectedStyle: null == selectedStyle ? _self.selectedStyle : selectedStyle // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,selectedTone: null == selectedTone ? _self.selectedTone : selectedTone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,selectedLength: null == selectedLength ? _self.selectedLength : selectedLength // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,response: null == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as ParaphraseResponseModel,history: null == history ? _self._history : history // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,favorites: null == favorites ? _self._favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,selectedOptionId: freezed == selectedOptionId ? _self.selectedOptionId : selectedOptionId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ParaphraseResponseModelCopyWith<$Res> get response {
  
  return $ParaphraseResponseModelCopyWith<$Res>(_self.response, (value) {
    return _then(_self.copyWith(response: value));
  });
}
}

/// @nodoc


class _Error implements ParaphraseState {
  const _Error({required this.inputText, required this.selectedStyle, required this.selectedTone, required this.selectedLength, required this.sourceLanguage, required this.targetLanguage, required this.errorMessage, final  List<ParaphraseHistoryModel> history = const [], final  List<ParaphraseHistoryModel> favorites = const [], this.errorCode}): _history = history,_favorites = favorites;
  

@override final  String inputText;
@override final  ParaphraseStyle selectedStyle;
@override final  ParaphraseTone selectedTone;
@override final  ParaphraseLength selectedLength;
@override final  String sourceLanguage;
@override final  String targetLanguage;
 final  String errorMessage;
 final  List<ParaphraseHistoryModel> _history;
@override@JsonKey() List<ParaphraseHistoryModel> get history {
  if (_history is EqualUnmodifiableListView) return _history;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_history);
}

 final  List<ParaphraseHistoryModel> _favorites;
@override@JsonKey() List<ParaphraseHistoryModel> get favorites {
  if (_favorites is EqualUnmodifiableListView) return _favorites;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_favorites);
}

 final  String? errorCode;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ErrorCopyWith<_Error> get copyWith => __$ErrorCopyWithImpl<_Error>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Error&&(identical(other.inputText, inputText) || other.inputText == inputText)&&(identical(other.selectedStyle, selectedStyle) || other.selectedStyle == selectedStyle)&&(identical(other.selectedTone, selectedTone) || other.selectedTone == selectedTone)&&(identical(other.selectedLength, selectedLength) || other.selectedLength == selectedLength)&&(identical(other.sourceLanguage, sourceLanguage) || other.sourceLanguage == sourceLanguage)&&(identical(other.targetLanguage, targetLanguage) || other.targetLanguage == targetLanguage)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&const DeepCollectionEquality().equals(other._history, _history)&&const DeepCollectionEquality().equals(other._favorites, _favorites)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}


@override
int get hashCode => Object.hash(runtimeType,inputText,selectedStyle,selectedTone,selectedLength,sourceLanguage,targetLanguage,errorMessage,const DeepCollectionEquality().hash(_history),const DeepCollectionEquality().hash(_favorites),errorCode);

@override
String toString() {
  return 'ParaphraseState.error(inputText: $inputText, selectedStyle: $selectedStyle, selectedTone: $selectedTone, selectedLength: $selectedLength, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, errorMessage: $errorMessage, history: $history, favorites: $favorites, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res> implements $ParaphraseStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) = __$ErrorCopyWithImpl;
@override @useResult
$Res call({
 String inputText, ParaphraseStyle selectedStyle, ParaphraseTone selectedTone, ParaphraseLength selectedLength, String sourceLanguage, String targetLanguage, String errorMessage, List<ParaphraseHistoryModel> history, List<ParaphraseHistoryModel> favorites, String? errorCode
});




}
/// @nodoc
class __$ErrorCopyWithImpl<$Res>
    implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

/// Create a copy of ParaphraseState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? inputText = null,Object? selectedStyle = null,Object? selectedTone = null,Object? selectedLength = null,Object? sourceLanguage = null,Object? targetLanguage = null,Object? errorMessage = null,Object? history = null,Object? favorites = null,Object? errorCode = freezed,}) {
  return _then(_Error(
inputText: null == inputText ? _self.inputText : inputText // ignore: cast_nullable_to_non_nullable
as String,selectedStyle: null == selectedStyle ? _self.selectedStyle : selectedStyle // ignore: cast_nullable_to_non_nullable
as ParaphraseStyle,selectedTone: null == selectedTone ? _self.selectedTone : selectedTone // ignore: cast_nullable_to_non_nullable
as ParaphraseTone,selectedLength: null == selectedLength ? _self.selectedLength : selectedLength // ignore: cast_nullable_to_non_nullable
as ParaphraseLength,sourceLanguage: null == sourceLanguage ? _self.sourceLanguage : sourceLanguage // ignore: cast_nullable_to_non_nullable
as String,targetLanguage: null == targetLanguage ? _self.targetLanguage : targetLanguage // ignore: cast_nullable_to_non_nullable
as String,errorMessage: null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,history: null == history ? _self._history : history // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,favorites: null == favorites ? _self._favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<ParaphraseHistoryModel>,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
