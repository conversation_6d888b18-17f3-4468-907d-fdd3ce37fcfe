import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/paraphrase/models/index.dart';

part 'paraphrase_state.freezed.dart';

/// State for paraphrase feature with different states
@freezed
class ParaphraseState with _$ParaphraseState {
  /// Initial state when no operation is in progress
  const factory ParaphraseState.initial({
    @Default('') String inputText,
    @Default(ParaphraseStyle.simple) ParaphraseStyle selectedStyle,
    @Default(ParaphraseTone.neutral) ParaphraseTone selectedTone,
    @Default(ParaphraseLength.similar) ParaphraseLength selectedLength,
    @Default('en') String sourceLanguage,
    @Default('en') String targetLanguage,
    @Default([]) List<ParaphraseHistoryModel> history,
    @Default([]) List<ParaphraseHistoryModel> favorites,
  }) = _Initial;

  /// Loading state when paraphrasing is in progress
  const factory ParaphraseState.loading({
    required String inputText,
    required ParaphraseStyle selectedStyle,
    required ParaphraseTone selectedTone,
    required ParaphraseLength selectedLength,
    required String sourceLanguage,
    required String targetLanguage,
    @Default([]) List<ParaphraseHistoryModel> history,
    @Default([]) List<ParaphraseHistoryModel> favorites,
    String? loadingMessage,
  }) = _Loading;

  /// Success state when paraphrasing is completed successfully
  const factory ParaphraseState.success({
    required String inputText,
    required ParaphraseStyle selectedStyle,
    required ParaphraseTone selectedTone,
    required ParaphraseLength selectedLength,
    required String sourceLanguage,
    required String targetLanguage,
    required ParaphraseResponseModel response,
    @Default([]) List<ParaphraseHistoryModel> history,
    @Default([]) List<ParaphraseHistoryModel> favorites,
    String? selectedOptionId,
  }) = _Success;

  /// Error state when paraphrasing fails
  const factory ParaphraseState.error({
    required String inputText,
    required ParaphraseStyle selectedStyle,
    required ParaphraseTone selectedTone,
    required ParaphraseLength selectedLength,
    required String sourceLanguage,
    required String targetLanguage,
    required String errorMessage,
    @Default([]) List<ParaphraseHistoryModel> history,
    @Default([]) List<ParaphraseHistoryModel> favorites,
    String? errorCode,
  }) = _Error;
}

/// Extension methods for ParaphraseState
extension ParaphraseStateX on ParaphraseState {
  /// Check if the state is loading
  bool get isLoading => this is _Loading;

  /// Check if the state has a successful response
  bool get hasResponse => this is _Success;

  /// Check if the state has an error
  bool get hasError => this is _Error;

  /// Get the current input text regardless of state
  String get currentInputText => when(
        initial: (inputText, _, __, ___, ____, _____, ______, _______) => inputText,
        loading: (inputText, _, __, ___, ____, _____, ______, _______, ________) => inputText,
        success: (inputText, _, __, ___, ____, _____, ______, _______, ________, _________) => inputText,
        error: (inputText, _, __, ___, ____, _____, ______, _______, ________, _________) => inputText,
      );

  /// Get the current history regardless of state
  List<ParaphraseHistoryModel> get currentHistory => when(
        initial: (_, __, ___, ____, _____, ______, history, _______) => history,
        loading: (_, __, ___, ____, _____, ______, history, _______, ________) => history,
        success: (_, __, ___, ____, _____, ______, _______, history, ________, _________) => history,
        error: (_, __, ___, ____, _____, ______, history, _______, ________, _________) => history,
      );

  /// Get the current favorites regardless of state
  List<ParaphraseHistoryModel> get currentFavorites => when(
        initial: (_, __, ___, ____, _____, ______, _______, favorites) => favorites,
        loading: (_, __, ___, ____, _____, ______, _______, favorites, ________) => favorites,
        success: (_, __, ___, ____, _____, ______, _______, ________, favorites, _________) => favorites,
        error: (_, __, ___, ____, _____, ______, _______, favorites, ________, _________) => favorites,
      );

  /// Check if input text is valid for paraphrasing
  bool get isInputValid => currentInputText.trim().isNotEmpty;

  /// Get word count of current input
  int get inputWordCount => currentInputText.trim().split(RegExp(r'\s+')).length;

  /// Get character count of current input
  int get inputCharacterCount => currentInputText.length;
}
