import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vid_ad_manager/vid_ad_manager.dart';
import 'package:vid_base_project/product/common_blocs/session_bloc/session_bloc.dart';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/utils/extensions.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_purchase_manager/vid_purchase_manager.dart' as pm;
import 'package:vid_purchase_manager/vid_purchase_manager.dart';
import 'package:vid_ui/vid_ui.dart';

class PaywallView extends StatefulWidget {
  const PaywallView({this.offeringId, super.key});

  final String? offeringId;

  @override
  State<PaywallView> createState() => _PaywallViewState();
}

class _PaywallViewState extends State<PaywallView> {
  bool isPurchased = false;

  Future<void> _updateUserFlow(BuildContext context) async {
    isPurchased = true;
    final sessionState = context.sessionState;
    final updatedUser = sessionState.userFlow.copyWith(isUserPremium: true);
    await LocalDbManager.instance.updateUserFlow(updatedUser);
    vidAdManager.updateAdsEnabled(enabled: false);
    context.sessionEvent(SessionEvent.updateUserFlow(updatedUser));
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 500), () async {
        if (!context.sessionState.userFlow.isFirstPaywallSeen) {
          final updatedFlow = await LocalDbManager.instance
              .updateUserFlow(
                context.sessionState.userFlow.copyWith(
                  firstPaywallSeenAt: DateTime.now(),
                ),
              );
          context.sessionEvent(SessionEvent.updateUserFlow(updatedFlow));
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: pm.PaywallView(
        offering: widget.offeringId != null
            ? VidPurchaseManager.instance.getOffering(widget.offeringId!)
            : null,
        displayCloseButton: true,
        onPurchaseCompleted:
            (
              pm.CustomerInfo customerInfo,
              pm.StoreTransaction storeTransaction,
            ) async {
              await _updateUserFlow(context);
            },
        onPurchaseError: (pm.PurchasesError error) => showVidErrorDialog(
          context,
          description: purchaseErrorTranslation(error.code.name),
        ),
        onRestoreCompleted: (pm.CustomerInfo customerInfo) async {
          if (customerInfo.entitlements.active.isEmpty) {
            unawaited(
              showVidErrorDialog(
                context,
                description: VidLocalizationKeys
                    .purchaseRestoreErrorNothingToRestore
                    .tr(),
              ),
            );
            return;
          } else {
            await _updateUserFlow(context);
            await showVidSuccessDialog(
              context,
              description: LocalizationKeys.premiumRestoreSuccessful.tr(
                context: context,
              ),
            );
            context.pop(isPurchased);
          }
        },
        onRestoreError: (pm.PurchasesError error) => showVidErrorDialog(
          context,
          description: purchaseErrorTranslation(error.code.name),
        ),
        onDismiss: () => context.pop(isPurchased),
      ),
    );
  }
}
