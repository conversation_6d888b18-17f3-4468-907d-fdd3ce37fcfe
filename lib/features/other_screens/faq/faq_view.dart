import 'package:flutter/material.dart';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_base_project/product/routing/paths/other_route/other_route_paths.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

class FaqView extends StatelessWidget {
  const FaqView({super.key});

  @override
  Widget build(BuildContext context) {
    return VidFaqView(
      onContact: () =>
          context.pushReplacementNamed(OtherRoutePaths.contactUs.name),
      faqItems: LocalizationKeys.faqs
          .getIndexedDuoStringList(context)
          .map((e) => (question: e.title, answer: e.description))
          .toList(),
    );
  }
}
