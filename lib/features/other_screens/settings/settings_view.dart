import 'package:flutter/material.dart';
import 'package:vid_base_project/features/other_screens/settings/settings_mixin.dart';
import 'package:vid_core/vid_core.dart';

class SettingsView extends StatelessWidget with SettingsMixin {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: FHeader.nested(
        title: Text(
          VidLocalizationKeys.generalSettingsTitle.tr(context: context),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FButton(
            style: FButtonStyle.ghost,
            onPress: () {},
            child: const Text(webSite),
          ),
          Text(
            VidAppInfo().versionToDisplay,
          ),
        ],
      ),
    );
  }
}
