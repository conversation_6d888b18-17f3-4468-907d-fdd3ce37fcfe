import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_secure_app/vid_secure_app.dart';
import 'package:vid_ui/vid_ui.dart';

mixin AdvancedSecurityMixin {
  Future<void> onAuthorizedSessionDurationChanged(BuildContext context) async {
    final authorizedSessionDuration =
        await showVidSingleSelectSheet<AuthorizedSessionDuration>(
          context: context,
          items: AuthorizedSessionDuration.values,
          title: AuthorizedSessionDuration.fieldTitle(context),
          label: (e) => e.localizedTitle(context),
          description: AuthorizedSessionDuration.fieldDescription(context),
          initialValue:
              secureAppController.securityModel.value.authorizedSessionDuration,
        );
    if (authorizedSessionDuration != null) {
      unawaited(
        secureAppController.updateAuthorizedSessionDuration(
          authorizedSessionDuration,
        ),
      );
    }
  }

  Future<void> onAutoLockOnInactivityChanged(BuildContext context) async {
    final autoLockOnInactivity =
        await showVidSingleSelectSheet<AutoLockOnInactivity>(
          context: context,
          items: AutoLockOnInactivity.values,
          title: AutoLockOnInactivity.fieldTitle(context),
          label: (e) => e.localizedTitle(context),
          description: AutoLockOnInactivity.fieldDescription(context),
          initialValue:
              secureAppController.securityModel.value.autoLockOnInactivity,
        );
    if (autoLockOnInactivity != null) {
      unawaited(
        secureAppController.updateAutoLockOnInactivity(autoLockOnInactivity),
      );
    }
  }

  Future<void> onAutoLockOnMinimizeChanged(BuildContext context) async {
    final autoLockOnMinimize =
        await showVidSingleSelectSheet<AutoLockOnMinimize>(
          context: context,
          items: AutoLockOnMinimize.values,
          title: AutoLockOnMinimize.fieldTitle(context),
          label: (e) => e.localizedTitle(context),
          description: AutoLockOnMinimize.fieldDescription(context),
          initialValue:
              secureAppController.securityModel.value.autoLockOnMinimize,
        );
    if (autoLockOnMinimize != null) {
      unawaited(
        secureAppController.updateAutoLockOnMinimize(autoLockOnMinimize),
      );
    }
  }
}
