import 'package:flutter/material.dart';
import 'package:vid_base_project/features/other_screens/advanced_security/advanced_security_mixin.dart';
import 'package:vid_base_project/product/utils/index.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_secure_app/vid_secure_app.dart';
import 'package:vid_ui/vid_ui.dart';

class AdvancedSecurityView extends StatelessWidget with AdvancedSecurityMixin {
  const AdvancedSecurityView({super.key});

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: VidNestedHeader(
        title: VidLocalizationKeys.advancedSecurityTitle.tr(context: context),
      ),
      child: Column(
        children: [
          FLabel(
            axis: Axis.vertical,
            description: Text(
              AuthorizedSessionDuration.fieldDescription(context),
            ),
            child: FTile(
              title: Text(AuthorizedSessionDuration.fieldTitle(context)),
              subtitle: ValueListenableBuilder(
                valueListenable: secureAppController.securityModel,
                builder: (context, value, child) => Text(
                  value.authorizedSessionDuration.localizedTitle(context),
                ),
              ),
              suffixIcon: const Icon(FIcons.chevronDown),
              prefixIcon: const Icon(FIcons.clock),
              onPress: () => onAuthorizedSessionDurationChanged(context),
            ),
          ),
          kLargeSpacing.toHeight,
          FLabel(
            axis: Axis.vertical,
            description: Text(AutoLockOnInactivity.fieldDescription(context)),
            child: FTile(
              title: Text(AutoLockOnInactivity.fieldTitle(context)),
              subtitle: ValueListenableBuilder(
                valueListenable: secureAppController.securityModel,
                builder: (context, value, child) =>
                    Text(value.autoLockOnInactivity.localizedTitle(context)),
              ),
              suffixIcon: const Icon(FIcons.chevronDown),
              prefixIcon: const Icon(FIcons.timer),
              onPress: () => onAutoLockOnInactivityChanged(context),
            ),
          ),
          kLargeSpacing.toHeight,
          FLabel(
            axis: Axis.vertical,
            description: Text(AutoLockOnMinimize.fieldDescription(context)),
            child: FTile(
              title: Text(AutoLockOnMinimize.fieldTitle(context)),
              subtitle: ValueListenableBuilder(
                valueListenable: secureAppController.securityModel,
                builder: (context, value, child) =>
                    Text(value.autoLockOnMinimize.localizedTitle(context)),
              ),
              suffixIcon: const Icon(FIcons.chevronDown),
              prefixIcon: const Icon(FIcons.lock),
              onPress: () => onAutoLockOnMinimizeChanged(context),
            ),
          ),
          kLargeSpacing.toHeight,
          FLabel(
            axis: Axis.vertical,
            description: Text(blurAppOnBackgroundDescription(context)),
            child: ValueListenableBuilder(
              valueListenable: secureAppController.securityModel,
              builder: (context, value, child) => FTile(
                title: Text(blurAppOnBackgroundTitle(context)),
                subtitle: ValueListenableBuilder(
                  valueListenable: secureAppController.securityModel,
                  builder: (context, value, child) =>
                      Text(onOffText(context, value: value.blurAppOnMinimize)),
                ),
                suffixIcon: SizedBox(
                  height: 20,
                  width: 50,
                  child: FSwitch(
                    value: value.blurAppOnMinimize,
                    onChange: secureAppController.updateBlurAppOnMinimize,
                  ),
                ),
                prefixIcon: const Icon(FIcons.eyeOff),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
