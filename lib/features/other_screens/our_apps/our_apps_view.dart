import 'package:flutter/material.dart';
import 'package:vid_base_project/config/config.dart';
import 'package:vid_in_app_review_manager/vid_in_app_review_manager.dart';
import 'package:vid_ui/vid_ui.dart';

class OurAppsView extends StatelessWidget {
  OurAppsView({super.key});

  // Sample data for apps
  final List<Map<String, dynamic>> apps = [
    {
      'icon': Icons.text_fields,
      'title': 'AI Paraphrase',
      'description': 'Transform your text with AI',
      'category': 'Productivity',
      'details':
          'Enhance your writing with AI-powered paraphrasing. Perfect for students, professionals, and content creators who want to improve their text quality.',
    },
    {
      'icon': Icons.camera_alt_outlined,
      'title': 'Photo Editor Pro',
      'description': 'Professional editing tools',
      'category': 'Photography',
      'details':
          'A powerful photo editor with professional-grade tools. Includes filters, effects, cropping tools, and advanced editing features for perfect photos every time.',
    },
    {
      'icon': Icons.lock_outline,
      'title': 'Password Vault',
      'description': 'Secure password manager ' * 3,
      'category': 'Security',
      'details':
          'Keep all your passwords secure with military-grade encryption. Generate strong passwords, auto-fill forms, and sync across all your devices.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return VidOurAppsView(
      apps: apps
          .map(
            (e) => VidAppModel(
              appId: kAppStoreId,
              appIcon: const FlutterLogo(size: 50),
              title: e['title'] as String,
              description: e['description'] as String,
              details: e['details'] as String,
            ),
          )
          .toList(),
      currentAppId: kAppStoreId,
      onTapDownload: (appId) =>
          VidInAppReviewManager.instance.openStore(appStoreId: appId),
      onTapRate: (appId) =>
          VidInAppReviewManager.instance.openStoreForRating(appStoreId: appId),
    );
  }
}
