import 'package:flutter/material.dart';
import 'package:vid_base_project/config/config.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

class ContactUsView extends StatelessWidget {
  const ContactUsView({super.key, this.subject});

  final VidContactUsSubject? subject;

  @override
  Widget build(BuildContext context) {
    return VidContactUsView(
      targetMail: kSupportMail,
      mailPrefix: kSupportMailPrefix,
      onTapFaq: () => context.pushReplacementNamed(OtherRoutePaths.faq.name),
      initialSubject: subject,
    );
  }
}
