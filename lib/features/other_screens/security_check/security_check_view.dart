import 'package:flutter/material.dart';
import 'package:vid_base_project/features/other_screens/security_check/security_check_mixin.dart';
import 'package:vid_base_project/product/utils/locator.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_secure_app/vid_secure_app.dart';
import 'package:vid_ui/vid_ui.dart';

// 2. Main Widget Class
class SecurityCheckView extends StatelessWidget with SecurityCheckMixin {
  const SecurityCheckView({super.key});

  List<VidSecurityCheckStep> steps(BuildContext context) => [
    if (secureAppController.canCheckBiometrics)
      VidSecurityCheckStep(
        icon: const Icon(FIcons.fingerprint),
        title: VidLocalizationKeys.generalSettingsBiometricsTitle.tr(
          context: context,
        ),
        subtitle: VidLocalizationKeys.securityCheckViewBiometricDescription.tr(
          context: context,
        ),
        isEnabled: secureAppController.isFaceIdCheckable,
        onTap: () => toggleFaceId(context),
      ),
    VidSecurityCheckStep(
      icon: const Icon(Icons.key_outlined),
      title: VidLocalizationKeys.generalSettingsPinLockTitle.tr(
        context: context,
      ),
      subtitle: VidLocalizationKeys.securityCheckViewPinDescription.tr(
        context: context,
      ),
      isEnabled: secureAppController.securityModel.value.isPinEnabled,
      onTap: () => togglePin(context),
    ),
    VidSecurityCheckStep(
      icon: const Icon(FIcons.clock),
      title: AuthorizedSessionDuration.fieldTitle(context),
      subtitle: AuthorizedSessionDuration.fieldDescription(context),
      isEnabled: secureAppController
          .securityModel
          .value
          .authorizedSessionDuration
          .isActive,
      onTap: () => onAuthorizedSessionDurationChanged(context),
    ),
    VidSecurityCheckStep(
      icon: const Icon(FIcons.eyeOff),
      title: blurAppOnBackgroundTitle(context),
      subtitle: blurAppOnBackgroundDescription(context),
      isEnabled: secureAppController.securityModel.value.blurAppOnMinimize,
      onTap: () => secureAppController.updateBlurAppOnMinimize(
        !secureAppController.securityModel.value.blurAppOnMinimize,
      ),
    ),
    VidSecurityCheckStep(
      icon: const Icon(FIcons.lock),
      title: AutoLockOnInactivity.fieldTitle(context),
      subtitle: AutoLockOnInactivity.fieldDescription(context),
      isEnabled:
          secureAppController.securityModel.value.autoLockOnInactivity.isActive,
      onTap: () => onAutoLockOnInactivityChanged(context),
    ),
    VidSecurityCheckStep(
      icon: const Icon(FIcons.lock),
      title: AutoLockOnMinimize.fieldTitle(context),
      subtitle: AutoLockOnMinimize.fieldDescription(context),
      isEnabled:
          secureAppController.securityModel.value.autoLockOnMinimize.isActive,
      onTap: () => onAutoLockOnMinimizeChanged(context),
    ),
  ];

  // 4. Build Method
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: secureAppController.securityModel,
      builder: (context, value, child) =>
          VidSecurityCheckView(steps: steps(context)),
    );
  }
}
