import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/local_database/models/app_utilities_model.dart';
import 'package:vid_ui/vid_ui.dart';

class AppNotifier {
  AppNotifier();

  late ValueNotifier<AppUtilitiesModel> appUtil;

  Future<void> initialize() async {
    final appUtil = await LocalDbManager.instance.getAppUtil();
    this.appUtil = ValueNotifier(appUtil ?? const AppUtilitiesModel());
  }

  Future<void> updateAppUtil(VidThemePickerResult result) async {
    appUtil.value = AppUtilitiesModel(
      themeMode: result.themeMode,
      theme: result.theme,
    );
    unawaited(LocalDbManager.instance.updateAppUtil(appUtil.value));
  }
}
