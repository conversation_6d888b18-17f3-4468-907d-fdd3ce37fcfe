import 'dart:async';

import 'package:quick_actions/quick_actions.dart';
import 'package:vid_base_project/product/constants/i10n_constants.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_base_project/product/utils/extensions.dart';
import 'package:vid_core/vid_core.dart';

class QuickActionsManager {
  QuickActionsManager();

  static const _quickActions = QuickActions();

  static List<QuickActionsModel> actions = [
    QuickActionsModel(
      type: QuickActionsType.contactUs.name,
      title: LocalizationKeys.quickActionsContactUsTitle.tr(),
      subtitle: LocalizationKeys.quickActionsContactUsSubtitle.tr(),
      onTap: () => AppRouter.context.pushNamed(OtherRoutePaths.contactUs.name),
    ),
    QuickActionsModel(
      type: QuickActionsType.specialOffer.name,
      title: LocalizationKeys.quickActionsSpecialOfferTitle.tr(),
      subtitle: LocalizationKeys.quickActionsSpecialOfferSubtitle.tr(),
      onTap: () => AppRouter.context.navigateToPremium(),
    ),
  ];

  Future<void> initialize() async {
    await _quickActions.initialize(_handleQuickAction);

    unawaited(
      _quickActions.setShortcutItems(
        actions
            .map(
              (e) => ShortcutItem(
                type: e.type,
                localizedTitle: e.title,
                localizedSubtitle: e.subtitle,
              ),
            )
            .toList(),
      ),
    );
  }

  void _handleQuickAction(String shortcutType) {
    final type = QuickActionsType.values.byName(shortcutType);
    actions.firstWhere((element) => element.type == type.name).onTap?.call();
  }
}

class QuickActionsModel {
  QuickActionsModel({
    required this.type,
    required this.title,
    required this.subtitle,
    this.onTap,
  });

  final String type;
  final String title;
  final String subtitle;
  final void Function()? onTap;
}

enum QuickActionsType { contactUs, specialOffer }
