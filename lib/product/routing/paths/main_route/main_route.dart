import 'package:vid_base_project/features/bottom_navigation_screens/index.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_base_project/product/routing/paths/main_route/main_route_paths.dart';
import 'package:vid_core/vid_core.dart';

/// Auth route
final class MainRoute {
  MainRoute._();

  /// Routes of the main route
  static RouteBase routes = StatefulShellRoute.indexedStack(
    builder:
        (context, state, navigationShell) => BottomNavigationBarWrapper(
          navigationShell: navigationShell,
          child: navigationShell,
        ),
    branches: [
      StatefulShellBranch(
        routes: [
          GoRoute(
            path: MainRoutePaths.home.path,
            name: MainRoutePaths.home.name,
            builder: (context, state) => const HomeView(),
          ),
        ],
      ),
      StatefulShellBranch(
        routes: [
          GoRoute(
            path: MainRoutePaths.history.path,
            name: MainRoutePaths.history.name,
            builder: (context, state) => const HistoryView(),
          ),
        ],
      ),
      StatefulShellBranch(
        routes: [
          GoRoute(
            path: MainRoutePaths.profile.path,
            name: MainRoutePaths.profile.name,
            builder: (context, state) => const ProfileView(),
          ),
        ],
      ),
    ],
  );
}
