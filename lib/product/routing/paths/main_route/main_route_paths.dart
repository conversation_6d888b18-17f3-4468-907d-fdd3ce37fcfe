
enum MainRoutePaths {
  /// Home screen
  home('/home'),

  /// Profile screen
  profile('/account'),

  /// History screen
  history('/history'),

  /// Agents screen
  agents('/agents');

  /// Creates a route path
  const MainRoutePaths(this.path);

  /// Path for the route
  final String path;

  /// Name for the route
  String get name =>
      path.replaceAll('/', '').isNotEmpty ? path.replaceAll('/', '') : '/';
}
