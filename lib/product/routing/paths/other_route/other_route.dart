import 'package:vid_base_project/features/other_screens/advanced_security/advanced_security_view.dart';
import 'package:vid_base_project/features/other_screens/contact_us/contact_us_view.dart';
import 'package:vid_base_project/features/other_screens/index.dart';
import 'package:vid_base_project/features/other_screens/paywall/paywall_view.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

final class OtherRoute {
  OtherRoute._();

  static List<RouteBase> routes = [
    GoRoute(
      path: OtherRoutePaths.securityCheck.path,
      name: OtherRoutePaths.securityCheck.name,
      pageBuilder: (context, state) {
        return const SecurityCheckView().subPageNavigationAnimation();
      },
    ),
    GoRoute(
      path: OtherRoutePaths.faq.path,
      name: OtherRoutePaths.faq.name,
      pageBuilder: (context, state) {
        return const FaqView().subPageNavigationAnimation();
      },
    ),
    GoRoute(
      path: OtherRoutePaths.contactUs.path,
      name: OtherRoutePaths.contactUs.name,
      pageBuilder: (context, state) {
        final args = state.extra as VidContactUsSubject?;
        return ContactUsView(subject: args).subPageNavigationAnimation();
      },
    ),
    GoRoute(
      path: OtherRoutePaths.ourApps.path,
      name: OtherRoutePaths.ourApps.name,
      pageBuilder: (context, state) {
        return  OurAppsView().subPageNavigationAnimation();
      },
    ),
    GoRoute(
      path: OtherRoutePaths.paywall.path,
      name: OtherRoutePaths.paywall.name,
      pageBuilder: (context, state) {
        return  const PaywallView().subPageNavigationAnimation();
      },
    ),
    GoRoute(
      path: OtherRoutePaths.advancedSecurity.path,
      name: OtherRoutePaths.advancedSecurity.name,
      pageBuilder: (context, state) {
        return    const AdvancedSecurityView().subPageNavigationAnimation();
      },
    ),
    GoRoute(
      path: OtherRoutePaths.settings.path,
      name: OtherRoutePaths.settings.name,
      pageBuilder: (context, state) {
        return const SettingsView().subPageNavigationAnimation();
      },
    ),
  ];
}
