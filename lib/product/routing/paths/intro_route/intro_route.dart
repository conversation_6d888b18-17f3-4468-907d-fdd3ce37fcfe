import 'package:vid_base_project/features/index.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_core/vid_core.dart';

/// Auth route
final class InitialRoute {
  InitialRoute._();

  /// Routes of the auth route
  static List<RouteBase> routes = [
    GoRoute(
      path: InitialRoutePaths.splash.path,
      name: InitialRoutePaths.splash.name,
      builder: (context, state) =>  SplashView(),
    ),
    GoRoute(
      path: InitialRoutePaths.onboarding.path,
      name: InitialRoutePaths.onboarding.name,
      builder: (context, state) => OnboardingView(),
    ),
  ];
}
