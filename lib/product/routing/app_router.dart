import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vid_base_project/product/routing/index.dart';
import 'package:vid_core/vid_core.dart';

/// Application router configuration
final class AppRouter {
  AppRouter._();

  /// Parent navigator key
  static final GlobalKey<NavigatorState> parentKey = GlobalKey();

  static BuildContext get context => parentKey.currentState!.context;

  static String get currentRouteName => router.routerDelegate.currentConfiguration.uri.toString();

  /// Router instance
  static GoRouter router = GoRouter(
    initialLocation: InitialRoutePaths.splash.path,
    observers: [NavigationHistoryObserver.instance],
    navigatorKey: parentKey,
    routes: [...InitialRoute.routes, ...OtherRoute.routes, MainRoute.routes],
    errorBuilder: (context, state) => const Text('404'),
  );


}

extension WidgetExtension on Widget {
  /// Whether to show the widget or not
  Widget isVisible({required bool condition, Widget Function()? widget}) {
    return condition ? this : (widget?.call() ?? const SizedBox.shrink());
  }

  Page<dynamic> subPageNavigationAnimation() {
    if (Platform.isIOS) {
      return CupertinoPage(child: this);
    }
    return MaterialPage(child: this);
  }

  Widget listViewItemAnimation({required int index}) {
    return this;
  }
}
