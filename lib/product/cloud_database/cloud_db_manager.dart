import 'package:vid_base_project/product/cloud_database/models/app_config_model.dart';
import 'package:vid_base_project/product/constants/app_constants.dart';
import 'package:vid_base_project/product/utils/locator.dart';

class CloudDbManager {
      
  Future<AppConfigModel> getAppConfig() async {
    try {
      final result = await firestoreService.getDocument(
        path: kCloudDbAppConfigPath,
      );
      if(result == null){
        throw Exception('App config not found');
      }
      return AppConfigModel.fromJson(result);
    } catch (e) {
      rethrow;
    }
  }


}
