// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppConfigModel {

 ApiKeyModel get apiKey;
/// Create a copy of AppConfigModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppConfigModelCopyWith<AppConfigModel> get copyWith => _$AppConfigModelCopyWithImpl<AppConfigModel>(this as AppConfigModel, _$identity);

  /// Serializes this AppConfigModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppConfigModel&&(identical(other.apiKey, apiKey) || other.apiKey == apiKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,apiKey);

@override
String toString() {
  return 'AppConfigModel(apiKey: $apiKey)';
}


}

/// @nodoc
abstract mixin class $AppConfigModelCopyWith<$Res>  {
  factory $AppConfigModelCopyWith(AppConfigModel value, $Res Function(AppConfigModel) _then) = _$AppConfigModelCopyWithImpl;
@useResult
$Res call({
 ApiKeyModel apiKey
});


$ApiKeyModelCopyWith<$Res> get apiKey;

}
/// @nodoc
class _$AppConfigModelCopyWithImpl<$Res>
    implements $AppConfigModelCopyWith<$Res> {
  _$AppConfigModelCopyWithImpl(this._self, this._then);

  final AppConfigModel _self;
  final $Res Function(AppConfigModel) _then;

/// Create a copy of AppConfigModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? apiKey = null,}) {
  return _then(_self.copyWith(
apiKey: null == apiKey ? _self.apiKey : apiKey // ignore: cast_nullable_to_non_nullable
as ApiKeyModel,
  ));
}
/// Create a copy of AppConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ApiKeyModelCopyWith<$Res> get apiKey {
  
  return $ApiKeyModelCopyWith<$Res>(_self.apiKey, (value) {
    return _then(_self.copyWith(apiKey: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _AppConfigModel implements AppConfigModel {
  const _AppConfigModel({required this.apiKey});
  factory _AppConfigModel.fromJson(Map<String, dynamic> json) => _$AppConfigModelFromJson(json);

@override final  ApiKeyModel apiKey;

/// Create a copy of AppConfigModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppConfigModelCopyWith<_AppConfigModel> get copyWith => __$AppConfigModelCopyWithImpl<_AppConfigModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppConfigModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppConfigModel&&(identical(other.apiKey, apiKey) || other.apiKey == apiKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,apiKey);

@override
String toString() {
  return 'AppConfigModel(apiKey: $apiKey)';
}


}

/// @nodoc
abstract mixin class _$AppConfigModelCopyWith<$Res> implements $AppConfigModelCopyWith<$Res> {
  factory _$AppConfigModelCopyWith(_AppConfigModel value, $Res Function(_AppConfigModel) _then) = __$AppConfigModelCopyWithImpl;
@override @useResult
$Res call({
 ApiKeyModel apiKey
});


@override $ApiKeyModelCopyWith<$Res> get apiKey;

}
/// @nodoc
class __$AppConfigModelCopyWithImpl<$Res>
    implements _$AppConfigModelCopyWith<$Res> {
  __$AppConfigModelCopyWithImpl(this._self, this._then);

  final _AppConfigModel _self;
  final $Res Function(_AppConfigModel) _then;

/// Create a copy of AppConfigModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? apiKey = null,}) {
  return _then(_AppConfigModel(
apiKey: null == apiKey ? _self.apiKey : apiKey // ignore: cast_nullable_to_non_nullable
as ApiKeyModel,
  ));
}

/// Create a copy of AppConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ApiKeyModelCopyWith<$Res> get apiKey {
  
  return $ApiKeyModelCopyWith<$Res>(_self.apiKey, (value) {
    return _then(_self.copyWith(apiKey: value));
  });
}
}


/// @nodoc
mixin _$ApiKeyModel {

 String get gptApiKey; String get geminiApiKey; String get deepSeekApiKey;
/// Create a copy of ApiKeyModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ApiKeyModelCopyWith<ApiKeyModel> get copyWith => _$ApiKeyModelCopyWithImpl<ApiKeyModel>(this as ApiKeyModel, _$identity);

  /// Serializes this ApiKeyModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ApiKeyModel&&(identical(other.gptApiKey, gptApiKey) || other.gptApiKey == gptApiKey)&&(identical(other.geminiApiKey, geminiApiKey) || other.geminiApiKey == geminiApiKey)&&(identical(other.deepSeekApiKey, deepSeekApiKey) || other.deepSeekApiKey == deepSeekApiKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,gptApiKey,geminiApiKey,deepSeekApiKey);

@override
String toString() {
  return 'ApiKeyModel(gptApiKey: $gptApiKey, geminiApiKey: $geminiApiKey, deepSeekApiKey: $deepSeekApiKey)';
}


}

/// @nodoc
abstract mixin class $ApiKeyModelCopyWith<$Res>  {
  factory $ApiKeyModelCopyWith(ApiKeyModel value, $Res Function(ApiKeyModel) _then) = _$ApiKeyModelCopyWithImpl;
@useResult
$Res call({
 String gptApiKey, String geminiApiKey, String deepSeekApiKey
});




}
/// @nodoc
class _$ApiKeyModelCopyWithImpl<$Res>
    implements $ApiKeyModelCopyWith<$Res> {
  _$ApiKeyModelCopyWithImpl(this._self, this._then);

  final ApiKeyModel _self;
  final $Res Function(ApiKeyModel) _then;

/// Create a copy of ApiKeyModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? gptApiKey = null,Object? geminiApiKey = null,Object? deepSeekApiKey = null,}) {
  return _then(_self.copyWith(
gptApiKey: null == gptApiKey ? _self.gptApiKey : gptApiKey // ignore: cast_nullable_to_non_nullable
as String,geminiApiKey: null == geminiApiKey ? _self.geminiApiKey : geminiApiKey // ignore: cast_nullable_to_non_nullable
as String,deepSeekApiKey: null == deepSeekApiKey ? _self.deepSeekApiKey : deepSeekApiKey // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ApiKeyModel implements ApiKeyModel {
  const _ApiKeyModel({required this.gptApiKey, required this.geminiApiKey, required this.deepSeekApiKey});
  factory _ApiKeyModel.fromJson(Map<String, dynamic> json) => _$ApiKeyModelFromJson(json);

@override final  String gptApiKey;
@override final  String geminiApiKey;
@override final  String deepSeekApiKey;

/// Create a copy of ApiKeyModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ApiKeyModelCopyWith<_ApiKeyModel> get copyWith => __$ApiKeyModelCopyWithImpl<_ApiKeyModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ApiKeyModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ApiKeyModel&&(identical(other.gptApiKey, gptApiKey) || other.gptApiKey == gptApiKey)&&(identical(other.geminiApiKey, geminiApiKey) || other.geminiApiKey == geminiApiKey)&&(identical(other.deepSeekApiKey, deepSeekApiKey) || other.deepSeekApiKey == deepSeekApiKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,gptApiKey,geminiApiKey,deepSeekApiKey);

@override
String toString() {
  return 'ApiKeyModel(gptApiKey: $gptApiKey, geminiApiKey: $geminiApiKey, deepSeekApiKey: $deepSeekApiKey)';
}


}

/// @nodoc
abstract mixin class _$ApiKeyModelCopyWith<$Res> implements $ApiKeyModelCopyWith<$Res> {
  factory _$ApiKeyModelCopyWith(_ApiKeyModel value, $Res Function(_ApiKeyModel) _then) = __$ApiKeyModelCopyWithImpl;
@override @useResult
$Res call({
 String gptApiKey, String geminiApiKey, String deepSeekApiKey
});




}
/// @nodoc
class __$ApiKeyModelCopyWithImpl<$Res>
    implements _$ApiKeyModelCopyWith<$Res> {
  __$ApiKeyModelCopyWithImpl(this._self, this._then);

  final _ApiKeyModel _self;
  final $Res Function(_ApiKeyModel) _then;

/// Create a copy of ApiKeyModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? gptApiKey = null,Object? geminiApiKey = null,Object? deepSeekApiKey = null,}) {
  return _then(_ApiKeyModel(
gptApiKey: null == gptApiKey ? _self.gptApiKey : gptApiKey // ignore: cast_nullable_to_non_nullable
as String,geminiApiKey: null == geminiApiKey ? _self.geminiApiKey : geminiApiKey // ignore: cast_nullable_to_non_nullable
as String,deepSeekApiKey: null == deepSeekApiKey ? _self.deepSeekApiKey : deepSeekApiKey // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
