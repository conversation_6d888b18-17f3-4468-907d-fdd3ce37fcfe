import 'package:vid_core/vid_core.dart';

part 'app_config_model.freezed.dart';
part 'app_config_model.g.dart';

@freezed
sealed class AppConfigModel with _$AppConfigModel {
  const factory AppConfigModel({required ApiKeyModel apiKey}) = _AppConfigModel;

  factory AppConfigModel.fromJson(Map<String, dynamic> json) =>
      _$AppConfigModelFromJson(json);
}

@freezed
sealed class ApiKeyModel with _$ApiKeyModel {
  const factory ApiKeyModel({
    required String gptApiKey,
    required String geminiApiKey,
    required String deepSeekApiKey,
  }) = _ApiKeyModel;

  factory ApiKeyModel.fromJson(Map<String, dynamic> json) =>
      _$ApiKeyModelFromJson(json);
}
