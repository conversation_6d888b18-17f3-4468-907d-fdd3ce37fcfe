import 'package:flutter/material.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_ui/vid_ui.dart';

part 'app_utilities_model.freezed.dart';
part 'app_utilities_model.g.dart';

@freezed
sealed class AppUtilitiesModel with _$AppUtilitiesModel {
  const factory AppUtilitiesModel({
    @Default(ThemeMode.system) ThemeMode themeMode,
    @Default(FThemeTypes.zinc) FThemeTypes theme,
  }) = _AppUtilitiesModel;

  const AppUtilitiesModel._();

  factory AppUtilitiesModel.fromJson(Map<String, dynamic> json) =>
      _$AppUtilitiesModelFromJson(json);

  FThemeData themeData(BuildContext context) {
    return theme.getTheme(isDark: themeMode.isCurrentDark(context));
  }
}
