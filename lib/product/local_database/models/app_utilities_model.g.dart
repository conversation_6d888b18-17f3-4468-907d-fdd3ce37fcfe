// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_utilities_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppUtilitiesModel _$AppUtilitiesModelFromJson(Map<String, dynamic> json) =>
    _AppUtilitiesModel(
      themeMode:
          $enumDecodeNullable(_$ThemeModeEnumMap, json['themeMode']) ??
          ThemeMode.system,
      theme:
          $enumDecodeNullable(_$FThemeTypesEnumMap, json['theme']) ??
          FThemeTypes.zinc,
    );

Map<String, dynamic> _$AppUtilitiesModelToJson(_AppUtilitiesModel instance) =>
    <String, dynamic>{
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'theme': _$FThemeTypesEnumMap[instance.theme]!,
    };

const _$ThemeModeEnumMap = {
  ThemeMode.system: 'system',
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
};

const _$FThemeTypesEnumMap = {
  FThemeTypes.zinc: 'zinc',
  FThemeTypes.slate: 'slate',
  FThemeTypes.red: 'red',
  FThemeTypes.rose: 'rose',
  FThemeTypes.orange: 'orange',
  FThemeTypes.green: 'green',
  FThemeTypes.blue: 'blue',
  FThemeTypes.yellow: 'yellow',
  FThemeTypes.violet: 'violet',
};
