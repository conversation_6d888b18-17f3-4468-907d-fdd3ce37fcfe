
import 'package:vid_core/vid_core.dart';

part 'user_flow_model.freezed.dart';
part 'user_flow_model.g.dart';

@freezed
sealed class UserFlowModel with _$UserFlowModel {
  const factory UserFlowModel({
    @Default(false) bool isUserPremium,
    @Default(0) int totalPaywallView,
    String? lastSeenAppVersion,
    @Default(true) bool isFirstLaunch,
    DateTime? firstPaywallSeenAt,
    @Default(0) int totalRewardTaken,
  }) = _UserFlowModel;

  const UserFlowModel._();

  factory UserFlowModel.fromJson(Map<String, dynamic> json) =>
      _$UserFlowModelFromJson(json);

  bool get isFirstPaywallSeen => firstPaywallSeenAt != null;


  Duration? get limitedOfferRemainingTime {
    const offerDuration = Duration(hours: 14,minutes: 53,seconds: 28);
    if (firstPaywallSeenAt == null) {
      return offerDuration;
    }
    final now = DateTime.now();
    final diff = now.difference(firstPaywallSeenAt!);
    if (diff > offerDuration) {
      return null;
    }
    return offerDuration - diff;
  }
}
