// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_utilities_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppUtilitiesModel {

 ThemeMode get themeMode; FThemeTypes get theme;
/// Create a copy of AppUtilitiesModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppUtilitiesModelCopyWith<AppUtilitiesModel> get copyWith => _$AppUtilitiesModelCopyWithImpl<AppUtilitiesModel>(this as AppUtilitiesModel, _$identity);

  /// Serializes this AppUtilitiesModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppUtilitiesModel&&(identical(other.themeMode, themeMode) || other.themeMode == themeMode)&&(identical(other.theme, theme) || other.theme == theme));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,themeMode,theme);

@override
String toString() {
  return 'AppUtilitiesModel(themeMode: $themeMode, theme: $theme)';
}


}

/// @nodoc
abstract mixin class $AppUtilitiesModelCopyWith<$Res>  {
  factory $AppUtilitiesModelCopyWith(AppUtilitiesModel value, $Res Function(AppUtilitiesModel) _then) = _$AppUtilitiesModelCopyWithImpl;
@useResult
$Res call({
 ThemeMode themeMode, FThemeTypes theme
});




}
/// @nodoc
class _$AppUtilitiesModelCopyWithImpl<$Res>
    implements $AppUtilitiesModelCopyWith<$Res> {
  _$AppUtilitiesModelCopyWithImpl(this._self, this._then);

  final AppUtilitiesModel _self;
  final $Res Function(AppUtilitiesModel) _then;

/// Create a copy of AppUtilitiesModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? themeMode = null,Object? theme = null,}) {
  return _then(_self.copyWith(
themeMode: null == themeMode ? _self.themeMode : themeMode // ignore: cast_nullable_to_non_nullable
as ThemeMode,theme: null == theme ? _self.theme : theme // ignore: cast_nullable_to_non_nullable
as FThemeTypes,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AppUtilitiesModel extends AppUtilitiesModel {
  const _AppUtilitiesModel({this.themeMode = ThemeMode.system, this.theme = FThemeTypes.zinc}): super._();
  factory _AppUtilitiesModel.fromJson(Map<String, dynamic> json) => _$AppUtilitiesModelFromJson(json);

@override@JsonKey() final  ThemeMode themeMode;
@override@JsonKey() final  FThemeTypes theme;

/// Create a copy of AppUtilitiesModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppUtilitiesModelCopyWith<_AppUtilitiesModel> get copyWith => __$AppUtilitiesModelCopyWithImpl<_AppUtilitiesModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppUtilitiesModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppUtilitiesModel&&(identical(other.themeMode, themeMode) || other.themeMode == themeMode)&&(identical(other.theme, theme) || other.theme == theme));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,themeMode,theme);

@override
String toString() {
  return 'AppUtilitiesModel(themeMode: $themeMode, theme: $theme)';
}


}

/// @nodoc
abstract mixin class _$AppUtilitiesModelCopyWith<$Res> implements $AppUtilitiesModelCopyWith<$Res> {
  factory _$AppUtilitiesModelCopyWith(_AppUtilitiesModel value, $Res Function(_AppUtilitiesModel) _then) = __$AppUtilitiesModelCopyWithImpl;
@override @useResult
$Res call({
 ThemeMode themeMode, FThemeTypes theme
});




}
/// @nodoc
class __$AppUtilitiesModelCopyWithImpl<$Res>
    implements _$AppUtilitiesModelCopyWith<$Res> {
  __$AppUtilitiesModelCopyWithImpl(this._self, this._then);

  final _AppUtilitiesModel _self;
  final $Res Function(_AppUtilitiesModel) _then;

/// Create a copy of AppUtilitiesModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? themeMode = null,Object? theme = null,}) {
  return _then(_AppUtilitiesModel(
themeMode: null == themeMode ? _self.themeMode : themeMode // ignore: cast_nullable_to_non_nullable
as ThemeMode,theme: null == theme ? _self.theme : theme // ignore: cast_nullable_to_non_nullable
as FThemeTypes,
  ));
}


}

// dart format on
