// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_flow_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserFlowModel _$UserFlowModelFromJson(Map<String, dynamic> json) =>
    _UserFlowModel(
      isUserPremium: json['isUserPremium'] as bool? ?? false,
      totalPaywallView: (json['totalPaywallView'] as num?)?.toInt() ?? 0,
      lastSeenAppVersion: json['lastSeenAppVersion'] as String?,
      isFirstLaunch: json['isFirstLaunch'] as bool? ?? true,
      firstPaywallSeenAt: json['firstPaywallSeenAt'] == null
          ? null
          : DateTime.parse(json['firstPaywallSeenAt'] as String),
      totalRewardTaken: (json['totalRewardTaken'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$UserFlowModelToJson(_UserFlowModel instance) =>
    <String, dynamic>{
      'isUserPremium': instance.isUserPremium,
      'totalPaywallView': instance.totalPaywallView,
      'lastSeenAppVersion': instance.lastSeenAppVersion,
      'isFirstLaunch': instance.isFirstLaunch,
      'firstPaywallSeenAt': instance.firstPaywallSeenAt?.toIso8601String(),
      'totalRewardTaken': instance.totalRewardTaken,
    };
