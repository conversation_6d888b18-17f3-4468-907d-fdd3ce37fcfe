// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_flow_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserFlowModel {

 bool get isUserPremium; int get totalPaywallView; String? get lastSeenAppVersion; bool get isFirstLaunch; DateTime? get firstPaywallSeenAt; int get totalRewardTaken;
/// Create a copy of UserFlowModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserFlowModelCopyWith<UserFlowModel> get copyWith => _$UserFlowModelCopyWithImpl<UserFlowModel>(this as UserFlowModel, _$identity);

  /// Serializes this UserFlowModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserFlowModel&&(identical(other.isUserPremium, isUserPremium) || other.isUserPremium == isUserPremium)&&(identical(other.totalPaywallView, totalPaywallView) || other.totalPaywallView == totalPaywallView)&&(identical(other.lastSeenAppVersion, lastSeenAppVersion) || other.lastSeenAppVersion == lastSeenAppVersion)&&(identical(other.isFirstLaunch, isFirstLaunch) || other.isFirstLaunch == isFirstLaunch)&&(identical(other.firstPaywallSeenAt, firstPaywallSeenAt) || other.firstPaywallSeenAt == firstPaywallSeenAt)&&(identical(other.totalRewardTaken, totalRewardTaken) || other.totalRewardTaken == totalRewardTaken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isUserPremium,totalPaywallView,lastSeenAppVersion,isFirstLaunch,firstPaywallSeenAt,totalRewardTaken);

@override
String toString() {
  return 'UserFlowModel(isUserPremium: $isUserPremium, totalPaywallView: $totalPaywallView, lastSeenAppVersion: $lastSeenAppVersion, isFirstLaunch: $isFirstLaunch, firstPaywallSeenAt: $firstPaywallSeenAt, totalRewardTaken: $totalRewardTaken)';
}


}

/// @nodoc
abstract mixin class $UserFlowModelCopyWith<$Res>  {
  factory $UserFlowModelCopyWith(UserFlowModel value, $Res Function(UserFlowModel) _then) = _$UserFlowModelCopyWithImpl;
@useResult
$Res call({
 bool isUserPremium, int totalPaywallView, String? lastSeenAppVersion, bool isFirstLaunch, DateTime? firstPaywallSeenAt, int totalRewardTaken
});




}
/// @nodoc
class _$UserFlowModelCopyWithImpl<$Res>
    implements $UserFlowModelCopyWith<$Res> {
  _$UserFlowModelCopyWithImpl(this._self, this._then);

  final UserFlowModel _self;
  final $Res Function(UserFlowModel) _then;

/// Create a copy of UserFlowModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isUserPremium = null,Object? totalPaywallView = null,Object? lastSeenAppVersion = freezed,Object? isFirstLaunch = null,Object? firstPaywallSeenAt = freezed,Object? totalRewardTaken = null,}) {
  return _then(_self.copyWith(
isUserPremium: null == isUserPremium ? _self.isUserPremium : isUserPremium // ignore: cast_nullable_to_non_nullable
as bool,totalPaywallView: null == totalPaywallView ? _self.totalPaywallView : totalPaywallView // ignore: cast_nullable_to_non_nullable
as int,lastSeenAppVersion: freezed == lastSeenAppVersion ? _self.lastSeenAppVersion : lastSeenAppVersion // ignore: cast_nullable_to_non_nullable
as String?,isFirstLaunch: null == isFirstLaunch ? _self.isFirstLaunch : isFirstLaunch // ignore: cast_nullable_to_non_nullable
as bool,firstPaywallSeenAt: freezed == firstPaywallSeenAt ? _self.firstPaywallSeenAt : firstPaywallSeenAt // ignore: cast_nullable_to_non_nullable
as DateTime?,totalRewardTaken: null == totalRewardTaken ? _self.totalRewardTaken : totalRewardTaken // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _UserFlowModel extends UserFlowModel {
  const _UserFlowModel({this.isUserPremium = false, this.totalPaywallView = 0, this.lastSeenAppVersion, this.isFirstLaunch = true, this.firstPaywallSeenAt, this.totalRewardTaken = 0}): super._();
  factory _UserFlowModel.fromJson(Map<String, dynamic> json) => _$UserFlowModelFromJson(json);

@override@JsonKey() final  bool isUserPremium;
@override@JsonKey() final  int totalPaywallView;
@override final  String? lastSeenAppVersion;
@override@JsonKey() final  bool isFirstLaunch;
@override final  DateTime? firstPaywallSeenAt;
@override@JsonKey() final  int totalRewardTaken;

/// Create a copy of UserFlowModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserFlowModelCopyWith<_UserFlowModel> get copyWith => __$UserFlowModelCopyWithImpl<_UserFlowModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserFlowModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserFlowModel&&(identical(other.isUserPremium, isUserPremium) || other.isUserPremium == isUserPremium)&&(identical(other.totalPaywallView, totalPaywallView) || other.totalPaywallView == totalPaywallView)&&(identical(other.lastSeenAppVersion, lastSeenAppVersion) || other.lastSeenAppVersion == lastSeenAppVersion)&&(identical(other.isFirstLaunch, isFirstLaunch) || other.isFirstLaunch == isFirstLaunch)&&(identical(other.firstPaywallSeenAt, firstPaywallSeenAt) || other.firstPaywallSeenAt == firstPaywallSeenAt)&&(identical(other.totalRewardTaken, totalRewardTaken) || other.totalRewardTaken == totalRewardTaken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isUserPremium,totalPaywallView,lastSeenAppVersion,isFirstLaunch,firstPaywallSeenAt,totalRewardTaken);

@override
String toString() {
  return 'UserFlowModel(isUserPremium: $isUserPremium, totalPaywallView: $totalPaywallView, lastSeenAppVersion: $lastSeenAppVersion, isFirstLaunch: $isFirstLaunch, firstPaywallSeenAt: $firstPaywallSeenAt, totalRewardTaken: $totalRewardTaken)';
}


}

/// @nodoc
abstract mixin class _$UserFlowModelCopyWith<$Res> implements $UserFlowModelCopyWith<$Res> {
  factory _$UserFlowModelCopyWith(_UserFlowModel value, $Res Function(_UserFlowModel) _then) = __$UserFlowModelCopyWithImpl;
@override @useResult
$Res call({
 bool isUserPremium, int totalPaywallView, String? lastSeenAppVersion, bool isFirstLaunch, DateTime? firstPaywallSeenAt, int totalRewardTaken
});




}
/// @nodoc
class __$UserFlowModelCopyWithImpl<$Res>
    implements _$UserFlowModelCopyWith<$Res> {
  __$UserFlowModelCopyWithImpl(this._self, this._then);

  final _UserFlowModel _self;
  final $Res Function(_UserFlowModel) _then;

/// Create a copy of UserFlowModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isUserPremium = null,Object? totalPaywallView = null,Object? lastSeenAppVersion = freezed,Object? isFirstLaunch = null,Object? firstPaywallSeenAt = freezed,Object? totalRewardTaken = null,}) {
  return _then(_UserFlowModel(
isUserPremium: null == isUserPremium ? _self.isUserPremium : isUserPremium // ignore: cast_nullable_to_non_nullable
as bool,totalPaywallView: null == totalPaywallView ? _self.totalPaywallView : totalPaywallView // ignore: cast_nullable_to_non_nullable
as int,lastSeenAppVersion: freezed == lastSeenAppVersion ? _self.lastSeenAppVersion : lastSeenAppVersion // ignore: cast_nullable_to_non_nullable
as String?,isFirstLaunch: null == isFirstLaunch ? _self.isFirstLaunch : isFirstLaunch // ignore: cast_nullable_to_non_nullable
as bool,firstPaywallSeenAt: freezed == firstPaywallSeenAt ? _self.firstPaywallSeenAt : firstPaywallSeenAt // ignore: cast_nullable_to_non_nullable
as DateTime?,totalRewardTaken: null == totalRewardTaken ? _self.totalRewardTaken : totalRewardTaken // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
