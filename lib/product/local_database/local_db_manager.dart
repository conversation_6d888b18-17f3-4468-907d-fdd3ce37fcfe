import 'dart:async';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:vid_base_project/product/constants/app_constants.dart';
import 'package:vid_base_project/product/local_database/models/app_utilities_model.dart';
import 'package:vid_base_project/product/local_database/models/index.dart';
import 'package:vid_core/vid_core.dart';

class LocalDbManager {
  LocalDbManager._();

  static final LocalDbManager instance = LocalDbManager._();

  final VidLocalDatabase _dbManager = VidLocalDatabase(
    DatabaseConfig(dbName: dotenv.env[dbNameEnvKey]!),
  );

  Future<void> init() async => _dbManager.init();

  Future<void> dropDb() async => _dbManager.dropDb();

  Future<AppUtilitiesModel?> getAppUtil() async =>
      _dbManager.getKeyValue<AppUtilitiesModel>(
        KeyValueTables.appUtilities.value,
        fromJson: AppUtilitiesModel.fromJson,
      );

  Future<void> updateAppUtil(AppUtilitiesModel appUtil) async => _dbManager
      .addKeyValue(KeyValueTables.appUtilities.value, appUtil.toJson());

  Future<void> deleteAppUtil() async =>
      _dbManager.deleteKeyValue(KeyValueTables.appUtilities.value);

  Future<UserFlowModel?> getUserFlow() async =>
      _dbManager.getKeyValue<UserFlowModel>(
        KeyValueTables.userFlow.value,
        fromJson: UserFlowModel.fromJson,
      );

  Future<UserFlowModel> updateUserFlow(UserFlowModel userFlow) async {
    await _dbManager.addKeyValue(
      KeyValueTables.userFlow.value,
      userFlow.toJson(),
    );
    return userFlow;
  }


}

// enum DbTables {
//   chats('chats');

//   const DbTables(this.value);

//   final String value;
// }

enum KeyValueTables {
  userFlow('userFlow'),
  appUtilities('app_utilities'),
  user('user');

  const KeyValueTables(this.value);

  final String value;
}
