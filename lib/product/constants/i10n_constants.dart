import 'package:flutter/material.dart';
import 'package:vid_core/vid_core.dart';

const supportedLocales = [
  Locale('en', 'US'),
  Locale('zh', 'CN'),
  Locale('es', 'ES'),
  Locale('hi', 'IN'),
  Locale('ar', 'SA'),
  Locale('pt', 'BR'),
  Locale('ru', 'RU'),
  Locale('ja', 'JP'),
  Locale('de', 'DE'),
  Locale('fr', 'FR'),
  Locale('it', 'IT'),
  Locale('tr', 'TR'),
  Locale('vi', 'VN'),
  Locale('ko', 'KR'),
  Locale('id', 'ID'),
];

const localizationPath = 'assets/translations';

const localizationPrefix = 'vid_base_project';

const fallbackLocale = Locale('en', 'US');

enum LocalizationKeys {
  splashDescription('splash.description'),

  quickActionsContactUsTitle('quick_actions.contact_us.title'),
  quickActionsContactUsSubtitle('quick_actions.contact_us.subtitle'),

  quickActionsSpecialOfferTitle('quick_actions.special_offer.title'),
  quickActionsSpecialOfferSubtitle('quick_actions.special_offer.subtitle'),

  onboardingWelcomeStepTitle('onboarding.welcome_step.title'),
  onboardingWelcomeStepDescription('onboarding.welcome_step.description'),

  onboardingReadyToUseStepTitle('onboarding.ready_to_use_step.title'),
  onboardingReadyToUseStepDescription(
    'onboarding.ready_to_use_step.description',
  ),

  onboardingPersonalizationStepTitle('onboarding.personalization_step.title'),
  onboardingPersonalizationStepDescription(
    'onboarding.personalization_step.description',
  ),
  onboardingPersonalizationSteps('onboarding.personalization_step.steps'),

  onboardingRateStepTitle('onboarding.rate_step.title'),
  onboardingRateStepDescription('onboarding.rate_step.description'),

  onboardingAnalyzeStepAnalyzingTitle(
    'onboarding.analyze_step.analyzing_title',
  ),
  onboardingAnalyzeStepAnalyzedTitle('onboarding.analyze_step.analyzed_title'),
  onboardingAnalyzeStepDescription('onboarding.analyze_step.description'),
  onboardingAnalyzeStepSteps('onboarding.analyze_step.steps'),

  bottomNavigationHome('bottom_navigation.home'),
  bottomNavigationHistory('bottom_navigation.history'),
  bottomNavigationProfile('bottom_navigation.profile'),

  premiumPremiumCtaTitle('premium.premium_cta_title'),
  premiumPremiumCtaDescription('premium.premium_cta_description'),
  premiumRestoreSuccessful('premium.restore_successful'),

  faqs('faqs'),

  shareText('share_text');

  const LocalizationKeys(this.value);

  final String value;

  String get fullKey => '$localizationPrefix.$value';

  String tr({BuildContext? context}) => fullKey.tr(context: context);

  String plu(num num, {required BuildContext? context}) =>
      plural(fullKey, num, context: context);

  String trWithArgs(List<String> args, {required BuildContext? context}) =>
      fullKey.tr(args: args, context: context);

  String trWithNamedArgs(Map<String, String> namedArgs, {required BuildContext? context}) =>
      fullKey.tr(namedArgs: namedArgs, context: context);

  List<String> getIndexedStringList(BuildContext context) {
    final list = <String>[];
    String indexedPath(int index) => '$fullKey.$index';
    var index = 0;
    while (true) {
      final question = indexedPath(index).trExists(context: context);
      if (!question) break;
      list.add(indexedPath(index).tr(context: context));
      index++;
    }

    return list;
  }

  List<({String title, String description})> getIndexedDuoStringList(
    BuildContext context, {
    String titleKey = 'title',
    String descriptionKey = 'description',
  }) {
    final list = <({String title, String description})>[];
    String titlePath(int index) => '$fullKey.$index.$titleKey';
    String descriptionPath(int index) => '$fullKey.$index.$descriptionKey';
    var index = 0;
    while (true) {
      final question = titlePath(index).trExists(context: context);
      if (!question) break;
      list.add((
        title: titlePath(index).tr(context: context),
        description: descriptionPath(index).tr(context: context),
      ));
      index++;
    }

    return list;
  }
}
