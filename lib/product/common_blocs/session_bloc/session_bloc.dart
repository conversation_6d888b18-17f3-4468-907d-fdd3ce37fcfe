
import 'package:vid_base_project/product/local_database/models/index.dart';
import 'package:vid_core/vid_core.dart';

part 'session_bloc.freezed.dart';
part 'session_event.dart';
part 'session_state.dart';

class SessionBloc extends Bloc<SessionEvent, SessionState> {
  SessionBloc() : super(const SessionState()) {
    on<_UpdateUserFlow>(_onUpdateUserFlow);

    on<_Init>(_onInit);

  }

  


  void _onInit(_Init event, Emitter<SessionState> emit) {
    emit(event.sessionState);
  }


  void _onUpdateUserFlow(_UpdateUserFlow event, Emitter<SessionState> emit) {
    emit(state.copyWith(userFlow: event.userFlow));
  }

}
