// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SessionEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SessionEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SessionEvent()';
}


}

/// @nodoc
class $SessionEventCopyWith<$Res>  {
$SessionEventCopyWith(SessionEvent _, $Res Function(SessionEvent) __);
}


/// @nodoc


class _Init implements SessionEvent {
  const _Init(this.sessionState);
  

 final  SessionState sessionState;

/// Create a copy of SessionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InitCopyWith<_Init> get copyWith => __$InitCopyWithImpl<_Init>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Init&&(identical(other.sessionState, sessionState) || other.sessionState == sessionState));
}


@override
int get hashCode => Object.hash(runtimeType,sessionState);

@override
String toString() {
  return 'SessionEvent.init(sessionState: $sessionState)';
}


}

/// @nodoc
abstract mixin class _$InitCopyWith<$Res> implements $SessionEventCopyWith<$Res> {
  factory _$InitCopyWith(_Init value, $Res Function(_Init) _then) = __$InitCopyWithImpl;
@useResult
$Res call({
 SessionState sessionState
});


$SessionStateCopyWith<$Res> get sessionState;

}
/// @nodoc
class __$InitCopyWithImpl<$Res>
    implements _$InitCopyWith<$Res> {
  __$InitCopyWithImpl(this._self, this._then);

  final _Init _self;
  final $Res Function(_Init) _then;

/// Create a copy of SessionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sessionState = null,}) {
  return _then(_Init(
null == sessionState ? _self.sessionState : sessionState // ignore: *****************************
as SessionState,
  ));
}

/// Create a copy of SessionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SessionStateCopyWith<$Res> get sessionState {
  
  return $SessionStateCopyWith<$Res>(_self.sessionState, (value) {
    return _then(_self.copyWith(sessionState: value));
  });
}
}

/// @nodoc


class _UpdateUserFlow implements SessionEvent {
  const _UpdateUserFlow(this.userFlow);
  

 final  UserFlowModel userFlow;

/// Create a copy of SessionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateUserFlowCopyWith<_UpdateUserFlow> get copyWith => __$UpdateUserFlowCopyWithImpl<_UpdateUserFlow>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateUserFlow&&(identical(other.userFlow, userFlow) || other.userFlow == userFlow));
}


@override
int get hashCode => Object.hash(runtimeType,userFlow);

@override
String toString() {
  return 'SessionEvent.updateUserFlow(userFlow: $userFlow)';
}


}

/// @nodoc
abstract mixin class _$UpdateUserFlowCopyWith<$Res> implements $SessionEventCopyWith<$Res> {
  factory _$UpdateUserFlowCopyWith(_UpdateUserFlow value, $Res Function(_UpdateUserFlow) _then) = __$UpdateUserFlowCopyWithImpl;
@useResult
$Res call({
 UserFlowModel userFlow
});


$UserFlowModelCopyWith<$Res> get userFlow;

}
/// @nodoc
class __$UpdateUserFlowCopyWithImpl<$Res>
    implements _$UpdateUserFlowCopyWith<$Res> {
  __$UpdateUserFlowCopyWithImpl(this._self, this._then);

  final _UpdateUserFlow _self;
  final $Res Function(_UpdateUserFlow) _then;

/// Create a copy of SessionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? userFlow = null,}) {
  return _then(_UpdateUserFlow(
null == userFlow ? _self.userFlow : userFlow // ignore: *****************************
as UserFlowModel,
  ));
}

/// Create a copy of SessionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserFlowModelCopyWith<$Res> get userFlow {
  
  return $UserFlowModelCopyWith<$Res>(_self.userFlow, (value) {
    return _then(_self.copyWith(userFlow: value));
  });
}
}

/// @nodoc
mixin _$SessionState {

 UserFlowModel get userFlow;
/// Create a copy of SessionState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SessionStateCopyWith<SessionState> get copyWith => _$SessionStateCopyWithImpl<SessionState>(this as SessionState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SessionState&&(identical(other.userFlow, userFlow) || other.userFlow == userFlow));
}


@override
int get hashCode => Object.hash(runtimeType,userFlow);

@override
String toString() {
  return 'SessionState(userFlow: $userFlow)';
}


}

/// @nodoc
abstract mixin class $SessionStateCopyWith<$Res>  {
  factory $SessionStateCopyWith(SessionState value, $Res Function(SessionState) _then) = _$SessionStateCopyWithImpl;
@useResult
$Res call({
 UserFlowModel userFlow
});


$UserFlowModelCopyWith<$Res> get userFlow;

}
/// @nodoc
class _$SessionStateCopyWithImpl<$Res>
    implements $SessionStateCopyWith<$Res> {
  _$SessionStateCopyWithImpl(this._self, this._then);

  final SessionState _self;
  final $Res Function(SessionState) _then;

/// Create a copy of SessionState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userFlow = null,}) {
  return _then(_self.copyWith(
userFlow: null == userFlow ? _self.userFlow : userFlow // ignore: *****************************
as UserFlowModel,
  ));
}
/// Create a copy of SessionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserFlowModelCopyWith<$Res> get userFlow {
  
  return $UserFlowModelCopyWith<$Res>(_self.userFlow, (value) {
    return _then(_self.copyWith(userFlow: value));
  });
}
}


/// @nodoc


class _SessionState extends SessionState {
  const _SessionState({this.userFlow = const UserFlowModel()}): super._();
  

@override@JsonKey() final  UserFlowModel userFlow;

/// Create a copy of SessionState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SessionStateCopyWith<_SessionState> get copyWith => __$SessionStateCopyWithImpl<_SessionState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SessionState&&(identical(other.userFlow, userFlow) || other.userFlow == userFlow));
}


@override
int get hashCode => Object.hash(runtimeType,userFlow);

@override
String toString() {
  return 'SessionState(userFlow: $userFlow)';
}


}

/// @nodoc
abstract mixin class _$SessionStateCopyWith<$Res> implements $SessionStateCopyWith<$Res> {
  factory _$SessionStateCopyWith(_SessionState value, $Res Function(_SessionState) _then) = __$SessionStateCopyWithImpl;
@override @useResult
$Res call({
 UserFlowModel userFlow
});


@override $UserFlowModelCopyWith<$Res> get userFlow;

}
/// @nodoc
class __$SessionStateCopyWithImpl<$Res>
    implements _$SessionStateCopyWith<$Res> {
  __$SessionStateCopyWithImpl(this._self, this._then);

  final _SessionState _self;
  final $Res Function(_SessionState) _then;

/// Create a copy of SessionState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userFlow = null,}) {
  return _then(_SessionState(
userFlow: null == userFlow ? _self.userFlow : userFlow // ignore: *****************************
as UserFlowModel,
  ));
}

/// Create a copy of SessionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserFlowModelCopyWith<$Res> get userFlow {
  
  return $UserFlowModelCopyWith<$Res>(_self.userFlow, (value) {
    return _then(_self.copyWith(userFlow: value));
  });
}
}

// dart format on
