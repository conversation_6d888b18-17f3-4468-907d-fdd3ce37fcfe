import 'package:flutter/material.dart';
import 'package:vid_base_project/product/common_blocs/index.dart';
import 'package:vid_base_project/product/routing/index.dart';
// import 'package:vid_ad_manager/vid_ad_manager.dart';
import 'package:vid_core/vid_core.dart';

extension ContextExtension on BuildContext {
  SessionState get sessionState => read<SessionBloc>().state;
  void sessionEvent(SessionEvent event) => read<SessionBloc>().add(event);

  bool get isUserPremium => sessionState.isUserPremium;

  Future<bool> navigateToPremium({String? offeringId}) async {
    if (isUserPremium) {
      return true;
    }

    final res = await pushNamed(
      OtherRoutePaths.paywall.name,
      extra: offeringId,
    );
    return res == true;
  }
}
