import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:vid_ad_manager/vid_ad_manager.dart';
import 'package:vid_ai_service/vid_ai_service.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_base_project/product/managers/app_notifier.dart';
import 'package:vid_core/vid_core.dart';
import 'package:vid_firebase_wrapper/vid_firebase_wrapper.dart';
import 'package:vid_secure_app/vid_secure_app.dart';

final aiService = GetIt.instance.get<VidAiService>();

final analyticsService = GetIt.instance.get<VidFirebaseAnalyticsService>();

final firestoreService = GetIt.instance.get<VidFirebaseFirestoreService>();

final secureAppController = GetIt.instance.get<VidSecureAppController>();

final appNotifier = GetIt.instance.get<AppNotifier>();

VidAdConfig get adConfig => VidAdConfig(
  androidBannerId: dotenv.env[adUnitIdsAndroidBannerEnvKey],
  iosBannerId: dotenv.env[adUnitIdsIosBannerEnvKey],
  androidInterstitialId: dotenv.env[adUnitIdsAndroidInterstitialEnvKey],
  iosInterstitialId: dotenv.env[adUnitIdsIosInterstitialEnvKey],
  androidRewardedId: dotenv.env[adUnitIdsAndroidRewardedEnvKey],
  iosRewardedId: dotenv.env[adUnitIdsIosRewardedEnvKey],
);
