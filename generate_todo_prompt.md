You are an AI tasked with updating the `project_todo.md` file that is located at the root of the project for a mobile app development project. The file contains two sections: **Initial Setup Tasks** and **Project Specific Tasks**. The **Initial Setup Tasks** section contains pre-defined tasks related to the base project setup and must remain unchanged. Your task is to generate and fill only the **Project Specific Tasks** section based on the request.

**User Input**

The user will provide the following information:
- **App Idea**: AI Paraphrase
- **Feature Requests**: The only request from my side is data handling will be locally. No writing to the cloud. Rest of the features You Decide

**Instructions**

1. Read the existing `project_todo.md` file.
2. Do not modify the **Initial Setup Tasks** section.
3. Based on the user's **App Idea** and **Feature Requests**, generate a list of actionable tasks for the **Project Specific Tasks** section. Ensure the tasks are:
   - Specific to the user's app idea and requested features.
   - Clear, concise, and formatted as a checklist (e.g., `- [ ] Task description`).
   - Prioritized logically (e.g., core functionality first, then additional features).
   - Aligned with mobile app development best practices.
4. If the **Project Specific Tasks** section already contains tasks, append new tasks or update existing ones only if they align with the user's input, ensuring no duplication.
5. If the user provides vague or incomplete information or you need clarification , ask the user to provide more information until everything is clear. 
6. Update the `project_todo.md` file with the **Initial Setup Tasks** unchanged and the **Project Specific Tasks** section updated with the new tasks.