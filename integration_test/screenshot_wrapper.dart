import 'package:flutter/material.dart';
import 'package:vid_base_project/product/common_blocs/index.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_core/vid_core.dart';

/// A wrapper widget for screenshot testing that allows changing the child and locale
/// Use the static methods to access the state from outside
class ScreenshotWrapper extends StatefulWidget {
  const ScreenshotWrapper({super.key, this.child, this.locale});

  final Widget Function(BuildContext context)? child;
  final Locale? locale;

  /// Global key to access the state from outside
  static final GlobalKey<_ScreenshotWrapperState> globalKey =
      GlobalKey<_ScreenshotWrapperState>();

  /// Changes the current child widget
  static void changeChild(Widget Function(BuildContext context) newChild) {
    globalKey.currentState?.changeChild(newChild);
  }

  /// Changes the current locale
  static void changeLocale(Locale newLocale) {
    globalKey.currentState?.changeLocale(newLocale);
  }

  @override
  State<ScreenshotWrapper> createState() => _ScreenshotWrapperState();
}

class _ScreenshotWrapperState extends State<ScreenshotWrapper> {
  late Widget Function(BuildContext context) _currentChild;
  late Locale _currentLocale;
  BuildContext? _localeContext;

  @override
  void initState() {
    super.initState();
    _currentChild = widget.child ?? (context) => const SizedBox();
    _currentLocale = widget.locale ?? fallbackLocale;
  }

  /// Changes the current child widget
  void changeChild(Widget Function(BuildContext context) newChild) {
    setState(() {
      _currentChild = newChild;
    });
  }

  /// Changes the current locale
  void changeLocale(Locale newLocale) {
    setState(() {
      _currentLocale = newLocale;
    });
    _localeContext?.setLocale(newLocale);
    debugPrint('Current Locale: ${_localeContext?.locale.toStringWithSeparator()}');
  }

  Locale get locale => _localeContext?.locale ?? fallbackLocale;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => SessionBloc(), lazy: false),
      ],
      child: EasyLocalization(
        assetLoader: MultiAssetLoader([
          JsonAssetLoader(overridePath: vidLocalizationPath),
          JsonAssetLoader(overridePath: localizationPath),
        ]),
        supportedLocales: supportedLocales,
        path: kDefaultAppLocalizationPath,
        fallbackLocale: fallbackLocale,
        saveLocale: false,
        startLocale: _currentLocale,
        child: Builder(
          builder: (context) {
            return MaterialApp(
              debugShowCheckedModeBanner: false,
              localizationsDelegates: context.localizationDelegates,
              supportedLocales: context.supportedLocales,
              locale: _currentLocale,
              theme: FThemes.zinc.light.toApproximateMaterialTheme(),
              title: 'Screenshot Test',
              builder: (context, child) {
                _localeContext ??= context;
                return FTheme(
                  data: FThemes.zinc.light,
                  child: _currentChild(context),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
