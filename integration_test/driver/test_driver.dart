import 'dart:io';
import 'package:integration_test/integration_test_driver_extended.dart';

Future<void> main() async {
  try {
    await integrationDriver(
      onScreenshot:
          (
            String screenshotName,
            List<int> screenshotBytes, [
            Map<String, Object?>? args,
          ]) async {
            final image = await File(
              'screenshots/$screenshotName.png',
            ).create(recursive: true);
            image.writeAsBytesSync(screenshotBytes);
            return true;
          },
    );
  } catch (e) {
    print('Error occured: $e');
  }
}
