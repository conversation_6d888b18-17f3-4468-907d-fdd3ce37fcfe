import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:vid_base_project/bootstrap/app_initializer.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/bottom_navigation_bar_wrapper.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/history/history_view.dart';
import 'package:vid_base_project/features/bottom_navigation_screens/home/<USER>';
import 'package:vid_base_project/features/bottom_navigation_screens/profile/profile_view.dart';
import 'package:vid_base_project/features/other_screens/advanced_security/advanced_security_view.dart';
import 'package:vid_base_project/features/other_screens/contact_us/contact_us_view.dart';
import 'package:vid_base_project/features/other_screens/security_check/security_check_view.dart';
import 'package:vid_base_project/product/constants/index.dart';
import 'package:vid_base_project/product/local_database/local_db_manager.dart';
import 'package:vid_base_project/product/local_database/models/user_flow_model.dart';
import 'package:vid_core/vid_core.dart';

import 'screenshot_wrapper.dart';

void main() {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    await dotenv.load(fileName: '.env.production');
    await AppInitializer.initialize();

    // Setup premium user
    final userFlow =
        await LocalDbManager.instance.getUserFlow() ?? const UserFlowModel();
    await LocalDbManager.instance.updateUserFlow(
      userFlow.copyWith(isFirstLaunch: false, isUserPremium: true),
    );
  });

  final screenshots = [
    ScreenshotScreenItem(
      screenshotName: 'home',
      builder: (context) => VidBottomNavigationBar(
        currentIndex: 0,
        navigateToIndex: (_) {},
        destinations: destinations(context),
        child: const HomeView(),
      ),
    ),
    ScreenshotScreenItem(
      screenshotName: 'history',
      builder: (context) => VidBottomNavigationBar(
        currentIndex: 1,
        navigateToIndex: (_) {},
        destinations: destinations(context),
        child: const HistoryView(),
      ),
    ),
    ScreenshotScreenItem(
      screenshotName: 'profile',
      builder: (context) => VidBottomNavigationBar(
        currentIndex: 2,
        navigateToIndex: (_) {},
        destinations: destinations(context),
        child: const ProfileView(),
      ),
    ),
    ScreenshotScreenItem(
      screenshotName: 'security-checks',
      builder: (context) => const SecurityCheckView(),
    ),
    ScreenshotScreenItem(
      screenshotName: 'advanced-security',
      builder: (context) => const AdvancedSecurityView(),
    ),
    ScreenshotScreenItem(
      screenshotName: 'contact-us',
      builder: (context) => const ContactUsView(),
    ),
  ];

  /// Helper function to render a widget and take a screenshot
  Future<void> renderAndScreenshot(
    WidgetTester tester,
    List<ScreenshotScreenItem> items,
    List<Locale> locales,
  ) async {
    // Wrap widget in a MaterialApp with proper theme and locale
    await tester.pumpWidget(
      ScreenshotWrapper(
        key: ScreenshotWrapper.globalKey,
        child: (context) => items.first.builder(context),
        locale: locales.first,
      ),
    );

    // Make sure the initial widget is fully rendered
    await tester.pumpAndSettle();


    await Future.forEach(items, (item) async {

      ScreenshotWrapper.changeChild((context) => item.builder(context));
      await tester.pump();
      await Future<void>.delayed(const Duration(milliseconds: 500));

      await Future.forEach(locales, (locale) async {
        debugPrint('locale Setted to: ${locale.toStringWithSeparator()}');
          ScreenshotWrapper.changeLocale(locale);
          
          await tester.pump();
          await Future<void>.delayed(const Duration(milliseconds: 500));

          final screenshotName =
              '${localeTag(locale)}/${item.screenshotName}';

          debugPrint('Writing screenshot: $screenshotName');
          
          await binding.takeScreenshot(screenshotName);

          await Future<void>.delayed(const Duration(milliseconds: 500));
      });
    });
  }

  group('Take screenshots', () {
    testWidgets('Take screenshots of all screens in all locales', (
      WidgetTester tester,
    ) async {
      Timer.periodic(const Duration(seconds: 1), (timer) {
        debugPrint('TEST RUNNING FOR: ${timer.tick} SECONDS');
      });
      // Convert to image surface before starting the test
      await binding.convertFlutterSurfaceToImage();

      // Render and take screenshots
      await renderAndScreenshot(tester, screenshots, supportedLocales);
    });
  });
}

String localeTag(Locale locale) {
  return '${locale.languageCode}_${locale.countryCode}';
}

class ScreenshotScreenItem {
  ScreenshotScreenItem({required this.screenshotName, required this.builder});

  final String screenshotName;
  final Widget Function(BuildContext context) builder;
}
