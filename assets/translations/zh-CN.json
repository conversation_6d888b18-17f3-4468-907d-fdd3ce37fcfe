{"vid_base_project": {"splash": {"description": "最佳基础项目"}, "quick_actions": {"contact_us": {"title": "🤔 需要帮助？", "subtitle": "联系我们，我们的团队会迅速为您提供帮助。"}, "special_offer": {"title": "🎉 专属优惠等您来拿！", "subtitle": "以90%的折扣获得完整访问权限。"}}, "onboarding": {"welcome_step": {"title": "欢迎体验基础项目", "description": "用基础项目，快速打造您的应用"}, "ready_to_use_step": {"title": "开箱即用", "description": "稍作调整即可快速上线"}, "personalization_step": {"title": "个性化体验", "description": "开启数据跟踪，享受专属定制体验", "steps": {"0": {"title": "个性化功能 1", "description": "这是个性化功能 1 的介绍"}, "1": {"title": "个性化功能 2", "description": "这是个性化功能 2 的介绍"}, "2": {"title": "个性化功能 3", "description": "这是个性化功能 3 的介绍"}, "3": {"title": "个性化功能 4", "description": "这是个性化功能 4 的介绍"}}}, "rate_step": {"title": "开发者怎么说", "description": "加入数千名满意开发者，体验我们基础项目的魅力"}, "analyze_step": {"analyzing_title": "正在分析您的资料", "analyzed_title": "资料分析完成", "steps": {"0": {"title": "分析步骤 1", "description": "这是分析步骤 1 的说明"}, "1": {"title": "分析步骤 2", "description": "这是分析步骤 2 的说明"}, "2": {"title": "分析步骤 3", "description": "这是分析步骤 3 的说明"}, "3": {"title": "分析步骤 4", "description": "这是分析步骤 4 的说明"}}}}, "bottom_navigation": {"home": "首页", "history": "历史记录", "profile": "个人资料"}, "premium": {"premium_cta_title": "势不可挡", "premium_cta_description": "解锁高级功能，提升您的使用体验", "restore_successful": "订阅已成功恢复，您现在可以使用所有高级功能。"}, "faqs": {"0": {"title": "什么是基础项目？", "description": "基础项目是一款免费的移动应用模板，助您快速启动应用开发。"}, "1": {"title": "基础项目有哪些功能？", "description": "支持 RevenueCat、AdMob、Firebase 等多种功能。"}, "2": {"title": "如何成为高级用户？", "description": "在应用开发过程中，您可以通过将会话状态中的 isPremium 设置为 true 来升级为高级用户。"}, "3": {"title": "如何启用广告？", "description": "您需要配置 Google AdMob，并在 .env 文件中更改广告 ID 值，同时在 info.plist 文件中设置您的应用 ID。"}, "4": {"title": "支持哪些语言？", "description": "支持多种语言，我们会不断更新语言列表，目前支持 14 种语言。"}, "5": {"title": "如何启用 Firebase？", "description": "您需要拥有 Firebase 账户并在应用中配置 Firebase。推荐使用 FlutterFire CLI 命令行工具来帮助您完成 Firebase 设置。"}, "6": {"title": "支持哪些主题？", "description": "支持 ForUI 提供的所有默认主题。"}, "7": {"title": "是否支持身份验证？", "description": "目前仅支持本地身份验证。您可以集成 Firebase Auth 或其他身份验证服务，推荐使用 vid_firebase_wrapper 包。"}}, "share_text": "嘿！我正在用 {}，就像一个几乎完成的项目。快来试试，感受它的神奇吧！"}}