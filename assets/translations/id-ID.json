{"vid_base_project": {"splash": {"description": "Proyek Dasar Terbaik"}, "quick_actions": {"contact_us": {"title": "🤔 Butuh bantuan?", "subtitle": "<PERSON><PERSON><PERSON><PERSON> kami, tim kami akan membantu <PERSON>a dengan cepat."}, "special_offer": {"title": "🎉 Penawaran Spesial untuk Anda!", "subtitle": "Dapatkan akses penuh dengan diskon 90%."}}, "onboarding": {"welcome_step": {"title": "Selamat Datang di Proyek <PERSON>ar", "description": "Kembangkan aplikasi Anda dalam hitungan menit dengan Proyek <PERSON>"}, "ready_to_use_step": {"title": "Siap Digunakan", "description": "Terapkan dengan cepat setelah pen<PERSON>ua<PERSON> kecil"}, "personalization_step": {"title": "<PERSON><PERSON><PERSON>", "description": "Aktifkan pelacakan untuk pengalaman yang dipersonalisasi", "steps": {"0": {"title": "Fitur Personalisasi 1", "description": "Ini adalah deskripsi fitur personalisasi 1"}, "1": {"title": "Fitur Personalisasi 2", "description": "Ini adalah deskripsi fitur personalisasi 2"}, "2": {"title": "Fitur Personalisasi 3", "description": "Ini adalah deskripsi fitur personalisasi 3"}, "3": {"title": "Fitur Personalisasi 4", "description": "Ini adalah deskripsi fitur personalisasi 4"}}}, "rate_step": {"title": "Apa Kata Pengembang", "description": "Bergabunglah dengan ribuan pengembang puas yang menyukai proyek dasar kami"}, "analyze_step": {"analyzing_title": "Menganalisis Profil", "analyzed_title": "<PERSON>il <PERSON>", "steps": {"0": {"title": "Langkah Analisis 1", "description": "Ini adalah <PERSON>si langkah analisis 1"}, "1": {"title": "Langkah Analisis 2", "description": "<PERSON>i adalah <PERSON>si langkah analisis 2"}, "2": {"title": "Langkah Analisis 3", "description": "<PERSON>i adalah <PERSON>si langkah analisis 3"}, "3": {"title": "Langkah <PERSON>lisis 4", "description": "<PERSON>i adalah <PERSON>si langkah analisis 4"}}}}, "bottom_navigation": {"home": "Be<PERSON><PERSON>", "history": "Riwayat", "profile": "Profil"}, "premium": {"premium_cta_title": "Jadilah Tak Terhentikan", "premium_cta_description": "Dapatkan fitur premium, tingkatkan pengalaman Anda", "restore_successful": "<PERSON><PERSON><PERSON> berhasil dipulihkan. Anda sekarang dapat menggunakan semua fitur premium."}, "faqs": {"0": {"title": "Apa itu Proyek <PERSON>ar?", "description": "Proyek <PERSON> adalah template aplikasi seluler gratis yang membantu Anda memulai proses pengembangan aplikasi."}, "1": {"title": "Apa saja fitur <PERSON>?", "description": "Banyak fitur seperti RevenueCat, AdMob, Firebase, dan la<PERSON><PERSON>."}, "2": {"title": "Bagaimana cara menjadi premium?", "description": "<PERSON><PERSON><PERSON> proses pen<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> menjadi premium dengan mengatur isPremium ke true pada status sesi."}, "3": {"title": "Bagaimana cara mengaktifkan iklan?", "description": "Anda perlu menyiapkan Google AdMob dan mengubah nilai ID iklan di file .env. Anda juga perlu mengatur ID aplikasi di file info.plist."}, "4": {"title": "Bahasa apa saja yang didukung?", "description": "<PERSON><PERSON>k bahasa yang didukung. <PERSON><PERSON> terus memperbarui daftar bahasa. <PERSON>at ini, 14 bahasa didukung."}, "5": {"title": "Bagaimana cara mengaktifkan Firebase?", "description": "Anda perlu memiliki akun Firebase dan menyiapkan Firebase di aplikasi Anda. FlutterFire CLI adalah alat baris perintah yang direkomendasikan untuk membantu menyiapkan Firebase di aplikasi Anda."}, "6": {"title": "Tema apa saja yang didukung?", "description": "Semua tema default yang disediakan oleh ForUI didukung."}, "7": {"title": "<PERSON><PERSON><PERSON><PERSON> Anda mendukung autentikasi?", "description": "<PERSON>tuk saat ini, kami hanya mendukung autentikasi lokal. <PERSON>a dapat mengintegrasikan Firebase Auth atau layanan autentikasi lainnya. Paket vid_firebase_wrapper direkomendasikan."}}, "share_text": "Hai! Saya sedang menggunakan {}—seperti memiliki proyek yang hampir selesai. Coba sendiri dan lihat keajaibannya!"}}