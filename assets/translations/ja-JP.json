{"vid_base_project": {"splash": {"description": "最高のベースプロジェクト"}, "quick_actions": {"contact_us": {"title": "🤔 サポートが必要ですか？", "subtitle": "お気軽にご連絡ください。チームが迅速に対応します。"}, "special_offer": {"title": "🎉 あなただけの特別オファー！", "subtitle": "90%オフでフルアクセスをゲット！"}}, "onboarding": {"welcome_step": {"title": "ベースプロジェクトへようこそ", "description": "ベースプロジェクトで数分でアプリを開発"}, "ready_to_use_step": {"title": "すぐに使える", "description": "簡単なカスタマイズ後、迅速にデプロイ"}, "personalization_step": {"title": "パーソナライズ", "description": "トラッキングを有効にしてパーソナライズされた体験を", "steps": {"0": {"title": "パーソナライズ機能1", "description": "これはパーソナライズ機能の説明1です"}, "1": {"title": "パーソナライズ機能2", "description": "これはパーソナライズ機能の説明2です"}, "2": {"title": "パーソナライズ機能3", "description": "これはパーソナライズ機能の説明3です"}, "3": {"title": "パーソナライズ機能4", "description": "これはパーソナライズ機能の説明4です"}}}, "rate_step": {"title": "開発者の声", "description": "私たちのベースプロジェクトを愛する何千もの満足した開発者に加わろう"}, "analyze_step": {"analyzing_title": "プロフィール分析中", "analyzed_title": "プロフィール分析完了", "steps": {"0": {"title": "分析ステップ1", "description": "これは分析ステップの説明1です"}, "1": {"title": "分析ステップ2", "description": "これは分析ステップの説明2です"}, "2": {"title": "分析ステップ3", "description": "これは分析ステップの説明3です"}, "3": {"title": "分析ステップ4", "description": "これは分析ステップの説明4です"}}}}, "bottom_navigation": {"home": "ホーム", "history": "履歴", "profile": "プロフィール"}, "premium": {"premium_cta_title": "無敵になろう", "premium_cta_description": "プレミアム機能を利用して、体験を向上", "restore_successful": "サブスクリプションが正常に復元されました。すべてのプレミアム機能が利用可能です。"}, "faqs": {"0": {"title": "ベースプロジェクトとは？", "description": "ベースプロジェクトは、アプリ開発プロセスを始めるのに役立つ無料のモバイルアプリテンプレートです。"}, "1": {"title": "ベースプロジェクトの機能は何ですか？", "description": "RevenueCat、AdMob、Firebaseなど、多くの機能があります。"}, "2": {"title": "プレミアムになるには？", "description": "アプリ開発プロセス中に、セッション状態でisPremiumをtrueに設定することでプレミアムになれます。"}, "3": {"title": "広告を有効にするには？", "description": "Google AdMobを設定し、.envファイルで広告IDの値を変更する必要があります。また、info.plistファイルでアプリIDを設定する必要があります。"}, "4": {"title": "どの言語がサポートされていますか？", "description": "多くの言語がサポートされています。言語リストは随時更新しています。現在、14の言語がサポートされています。"}, "5": {"title": "Firebaseを有効にするには？", "description": "Firebaseアカウントが必要で、アプリにFirebaseを設定する必要があります。FlutterFire CLIは、アプリにFirebaseを設定するのに役立つ推奨のコマンドラインツールです。"}, "6": {"title": "どのテーマがサポートされていますか？", "description": "ForUIが提供するすべてのデフォルトテーマがサポートされています。"}, "7": {"title": "認証はサポートされていますか？", "description": "現在、ローカル認証のみをサポートしています。Firebase Authやその他の認証サービスを統合できます。vid_firebase_wrapperパッケージが推奨されます。"}}, "share_text": "ねえ！{}を使ってるよ—ほとんど完成したプロジェクトみたい。試してみて、その魔法を自分で体験して！"}}