{"vid_base_project": {"splash": {"description": "Best Base Project"}, "quick_actions": {"contact_us": {"title": "🤔 Need Help?", "subtitle": "Reach out, and our team will assist you promptly."}, "special_offer": {"title": "🎉 Exclusive Offer Awaits!", "subtitle": "Unlock full access with a {}% discount."}}, "onboarding": {"welcome_step": {"title": "Welcome to Base Project", "description": "Develop your app in minutes with Base Project"}, "ready_to_use_step": {"title": "Ready to Use", "description": "Deploy quickly after small customizations"}, "personalization_step": {"title": "Personalization", "description": "Enable tracking to get personalized experience", "steps": {"0": {"title": "Personalized Feature 1", "description": "This is a personalized feature description 1"}, "1": {"title": "Personalized Feature 2", "description": "This is a personalized feature description 2"}, "2": {"title": "Personalized Feature 3", "description": "This is a personalized feature description 3"}, "3": {"title": "Personalized Feature 4", "description": "This is a personalized feature description 4"}}}, "rate_step": {"title": "What Developers Say", "description": "Join thousands of satisfied developers who love our base project"}, "analyze_step": {"analyzing_title": "Analyzing Profile", "analyzed_title": "Profile Analyzed", "steps": {"0": {"title": "Analyze Step 1", "description": "This is a analyze step description 1"}, "1": {"title": "Analyze Step 2", "description": "This is a analyze step description 2"}, "2": {"title": "Analyze Step 3", "description": "This is a analyze step description 3"}, "3": {"title": "Analyze Step 4", "description": "This is a analyze step description 4"}}}}, "bottom_navigation": {"home": "Home", "history": "History", "profile": "Profile", "paraphrase": "Paraphrase"}, "premium": {"premium_cta_title": "Be Unstoppable", "premium_cta_description": "Get premium features, enhance your experience", "restore_successful": "Subscription restored successfully. You can now use all premium features."}, "faqs": {"0": {"title": "What is the Base Project?", "description": "Base Project is a free mobile app template that helps you to kickstart your app development process."}, "1": {"title": "What are the features of the Base Project?", "description": "Many features like RevenueCat, AdMob, Firebase, and more."}, "2": {"title": "How to make myself premium?", "description": "During the app development process, you can make yourself premium by settings isPremium true in the session state."}, "3": {"title": "How to enable ads?", "description": "You need to setup Google AdMob and change the ad Id values in .env file. Also you need to set your app id in the info.plist file."}, "4": {"title": "Which languages are supported?", "description": "Many languages are supported. We keep updating the language list. For now 14 languages are supported."}, "5": {"title": "How to enable Firebase?", "description": "You need to have Firebase account and setup Firebase in your app. FlutterFire CLI is a recommended command-line tool that helps you to setup Firebase in your app."}, "6": {"title": "Which themes are supported?", "description": "All the default themes that ForUI provides are supported."}, "7": {"title": "Do you support Authentication?", "description": "For now, we only support local authentication. You can integrate Firebase Auth or any other authentication service. Recommended is vid_firebase_wrapper package."}}, "share_text": "Hey there! I’ve been using {}—it’s like having an almost ready project. Try it out and see the magic for yourself!", "paraphrase": {"input": {"title": "Enter Your Text", "hint": "Type or paste the text you want to paraphrase...", "word_count": "Words: {}", "character_count": "Characters: {}"}, "options": {"title": "Paraphrase Options", "style": {"label": "Style"}, "tone": {"label": "<PERSON><PERSON>"}, "length": {"label": "Length"}}, "button": {"paraphrase": "Paraphrase Text", "processing": "Processing...", "try_again": "Try Again"}, "results": {"title": "Paraphrased Results", "confidence": "Confidence: {}%"}, "error": {"title": "Paraphrase Failed"}, "style": {"formal": "Formal", "casual": "Casual", "creative": "Creative", "simple": "Simple"}, "tone": {"professional": "Professional", "friendly": "Friendly", "academic": "Academic", "neutral": "Neutral"}, "length": {"shorter": "Shorter", "similar": "Similar Length", "longer": "Longer"}, "picker": {"style": {"title": "Select Style"}, "tone": {"title": "Select Tone"}, "length": {"title": "Select Length"}, "source_language": {"title": "Select Source Language"}, "target_language": {"title": "Select Target Language"}}, "action": {"copied_to_clipboard": "Copied to clipboard", "copy_error": "Failed to copy text", "share_error": "Failed to share text", "pasted_from_clipboard": "Text pasted from clipboard", "clipboard_empty": "Clipboard is empty", "paste_error": "Failed to paste from clipboard", "input_cleared": "Input text cleared"}, "clear_confirmation": {"title": "Clear Input", "message": "Are you sure you want to clear the input text?", "confirm": "Clear"}}, "language": {"english": "English", "spanish": "Spanish", "french": "French", "german": "German", "italian": "Italian", "portuguese": "Portuguese", "russian": "Russian", "japanese": "Japanese", "korean": "Korean", "chinese": "Chinese"}, "general": {"cancel": "Cancel"}}}