{"vid_base_project": {"splash": {"description": "<PERSON><PERSON> Basisprojekt"}, "quick_actions": {"contact_us": {"title": "🤔 Brauchen Si<PERSON> Hi<PERSON>?", "subtitle": "Kontaktieren Sie uns, unser Team hilft Ihnen schnell weiter."}, "special_offer": {"title": "🎉 Exklusives Angebot für Sie!", "subtitle": "Sichern Sie sich vollen Zugriff mit 90% Rabatt."}}, "onboarding": {"welcome_step": {"title": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>ro<PERSON>", "description": "Entwickle deine App in Minuten mit dem Basisprojekt"}, "ready_to_use_step": {"title": "Einsatzbereit", "description": "Schnell bereitstellen nach kleinen Anpassungen"}, "personalization_step": {"title": "Personalisierung", "description": "Aktiviere Tracking für eine personalisierte Erfahrung", "steps": {"0": {"title": "Personalisierte Funktion 1", "description": "Dies ist eine Beschreibung der personalisierten Funktion 1"}, "1": {"title": "Personalisierte Funktion 2", "description": "Dies ist eine Beschreibung der personalisierten Funktion 2"}, "2": {"title": "Personalisierte Funktion 3", "description": "Dies ist eine Beschreibung der personalisierten Funktion 3"}, "3": {"title": "Personalisierte Funktion 4", "description": "Dies ist eine Beschreibung der personalisierten Funktion 4"}}}, "rate_step": {"title": "<PERSON> sagen", "description": "Sc<PERSON><PERSON>ße dich Tausenden zufriedener Entwickler an, die unser Basisprojekt lieben"}, "analyze_step": {"analyzing_title": "Profil wird analysiert", "analyzed_title": "<PERSON><PERSON> analys<PERSON>", "steps": {"0": {"title": "Analyse-Schritt 1", "description": "Dies ist eine Beschreibung des Analyse-Schritts 1"}, "1": {"title": "Analyse-Schritt 2", "description": "Dies ist eine Beschreibung des Analyse-Schritts 2"}, "2": {"title": "Analyse-Schritt 3", "description": "Dies ist eine Beschreibung des Analyse-Schritts 3"}, "3": {"title": "Analyse-Schritt 4", "description": "Dies ist eine Beschreibung des Analyse-Schritts 4"}}}}, "bottom_navigation": {"home": "Start", "history": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Profil"}, "premium": {"premium_cta_title": "<PERSON>uf<PERSON><PERSON><PERSON> sein", "premium_cta_description": "Erhalte Premium-Funktionen und verbessere deine Erfahrung", "restore_successful": "Abonnement erfolgreich wiederhergestellt. Du kannst jetzt alle Premium-Funktionen nutzen."}, "faqs": {"0": {"title": "Was ist das Basisprojekt?", "description": "Das Basisprojekt ist eine kostenlose Vorlage für mobile Apps, die dir hilft, deinen App-Entwicklungsprozess zu starten."}, "1": {"title": "Welche Funktionen bietet das Basisprojekt?", "description": "Viele Funktionen wie RevenueCat, AdMob, Firebase und mehr."}, "2": {"title": "Wie werde ich Premium-Mitglied?", "description": "Während des App-Entwicklungsprozesses kannst du Premium-Mitglied werden, indem du isPremium in der Sitzung auf true setzt."}, "3": {"title": "Wie aktiviere ich Werbung?", "description": "Du musst Google AdMob e<PERSON> und die Anzeigen-IDs in der .env-Datei ändern. Außerdem musst du deine App-ID in der info.plist-Datei festlegen."}, "4": {"title": "Welche Sprachen werden unterstützt?", "description": "Viele Sprachen werden unterstützt. Wir aktualisieren die Sprachenliste kontinuierlich. Derzeit werden 14 Sprachen unterstützt."}, "5": {"title": "Wie aktiviere ich Firebase?", "description": "Du benötigst ein Firebase-Konto und musst Firebase in deiner App einrichten. Das FlutterFire CLI ist ein empfohlenes Kommandozeilen-<PERSON>l, das dir hilft, Firebase in deiner App einzurichten."}, "6": {"title": "Welche Themen werden unterstützt?", "description": "Alle Standard-Themen, die ForUI bietet, werden unterstützt."}, "7": {"title": "Unterstützt ihr Authentifizierung?", "description": "Derzeit unterstützen wir nur lokale Authentifizierung. Du kannst Firebase Auth oder einen anderen Authentifizierungsdienst integrieren. Empfohlen wird das vid_firebase_wrapper-Paket."}}, "share_text": "Hey! Ich nutze {} – es ist, als hätte man ein fast fertiges Projekt. Probier es aus und erlebe die Magie selbst!"}}