{"vid_base_project": {"splash": {"description": "Лучший базовый проект"}, "quick_actions": {"contact_us": {"title": "🤔 Нужна помощь?", "subtitle": "Свяжитесь с нами, наша команда быстро вам поможет."}, "special_offer": {"title": "🎉 Специальное предложение для вас!", "subtitle": "Получите полный доступ со скидкой 90%."}}, "onboarding": {"welcome_step": {"title": "Добро пожаловать в Базовый проект", "description": "Разработайте свое приложение за минуты с Базовым проектом"}, "ready_to_use_step": {"title": "Готов к использованию", "description": "Быстро развертывайте после небольших доработок"}, "personalization_step": {"title": "Персонализация", "description": "Включите отслеживание для персонализированного опыта", "steps": {"0": {"title": "Персонализированная функция 1", "description": "Это описание персонализированной функции 1"}, "1": {"title": "Персонализированная функция 2", "description": "Это описание персонализированной функции 2"}, "2": {"title": "Персонализированная функция 3", "description": "Это описание персонализированной функции 3"}, "3": {"title": "Персонализированная функция 4", "description": "Это описание персонализированной функции 4"}}}, "rate_step": {"title": "Что говорят разработчики", "description": "Присоединяйтесь к тысячам довольных разработчиков, которые любят наш базовый проект"}, "analyze_step": {"analyzing_title": "Анализ профиля", "analyzed_title": "Профиль проанализирован", "steps": {"0": {"title": "Шаг анализа 1", "description": "Это описание шага анализа 1"}, "1": {"title": "<PERSON>аг анализа 2", "description": "Это описание шага анализа 2"}, "2": {"title": "<PERSON>аг анализа 3", "description": "Это описание шага анализа 3"}, "3": {"title": "<PERSON>аг анализа 4", "description": "Это описание шага анализа 4"}}}}, "bottom_navigation": {"home": "Главная", "history": "История", "profile": "Профиль"}, "premium": {"premium_cta_title": "Будь неудержим", "premium_cta_description": "Получите премиум-функции, улучшите свой опыт", "restore_successful": "Подписка успешно восстановлена. Теперь вы можете использовать все премиум-функции."}, "faqs": {"0": {"title": "Что такое Базовый проект?", "description": "Базовый проект — это бесплатный шаблон мобильного приложения, который помогает начать процесс разработки приложения."}, "1": {"title": "Какие функции есть в Базовом проекте?", "description": "Множество функций, таких как RevenueCat, AdMob, Firebase и другие."}, "2": {"title": "Как стать премиум-пользователем?", "description": "В процессе разработки приложения вы можете стать премиум-пользователем, установив isPremium в true в состоянии сессии."}, "3": {"title": "Как включить рекламу?", "description": "Вам нужно настроить Google AdMob и изменить значения идентификаторов рекламы в файле .env. Также необходимо указать идентификатор приложения в файле info.plist."}, "4": {"title": "Какие языки поддерживаются?", "description": "Поддерживается множество языков. Мы регулярно обновляем список языков. На данный момент поддерживается 14 языков."}, "5": {"title": "Как включить Firebase?", "description": "Вам нужен аккаунт Firebase и настройка Firebase в вашем приложении. FlutterFire CLI — это рекомендуемый инструмент командной строки, который помогает настроить Firebase в приложении."}, "6": {"title": "Какие темы поддерживаются?", "description": "Поддерживаются все стандартные темы, предоставляемые ForUI."}, "7": {"title": "Поддерживаете ли вы аутентификацию?", "description": "На данный момент поддерживается только локальная аутентификация. Вы можете интегрировать Firebase Auth или любую другую службу аутентификации. Рекомендуется пакет vid_firebase_wrapper."}}, "share_text": "Привет! Я использую {} — это как готовый проект. Попробуй сам и почувствуй магию!"}}