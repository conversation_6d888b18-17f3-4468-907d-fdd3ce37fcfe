{"vid_base_project": {"splash": {"description": "최고의 기본 프로젝트"}, "quick_actions": {"contact_us": {"title": "🤔 도움이 필요하세요?", "subtitle": "저희에게 연락주시면 팀이 빠르게 도와드릴게요."}, "special_offer": {"title": "🎉 당신을 위한 특별 제안!", "subtitle": "90% 할인으로 전체 액세스를 획득하세요."}}, "onboarding": {"welcome_step": {"title": "기본 프로젝트에 오신 것을 환영합니다", "description": "기본 프로젝트로 몇 분 만에 앱을 개발하세요"}, "ready_to_use_step": {"title": "바로 사용 가능", "description": "작은 커스터마이징 후 빠르게 배포하세요"}, "personalization_step": {"title": "개인화", "description": "추적을 활성화하여 맞춤형 경험을 누리세요", "steps": {"0": {"title": "맞춤 기능 1", "description": "이것은 맞춤 기능 설명 1입니다"}, "1": {"title": "맞춤 기능 2", "description": "이것은 맞춤 기능 설명 2입니다"}, "2": {"title": "맞춤 기능 3", "description": "이것은 맞춤 기능 설명 3입니다"}, "3": {"title": "맞춤 기능 4", "description": "이것은 맞춤 기능 설명 4입니다"}}}, "rate_step": {"title": "개발자들의 평가", "description": "우리 기본 프로젝트를 사랑하는 수천 명의 만족한 개발자들과 함께하세요"}, "analyze_step": {"analyzing_title": "프로필 분석 중", "analyzed_title": "프로필 분석 완료", "steps": {"0": {"title": "분석 단계 1", "description": "이것은 분석 단계 설명 1입니다"}, "1": {"title": "분석 단계 2", "description": "이것은 분석 단계 설명 2입니다"}, "2": {"title": "분석 단계 3", "description": "이것은 분석 단계 설명 3입니다"}, "3": {"title": "분석 단계 4", "description": "이것은 분석 단계 설명 4입니다"}}}}, "bottom_navigation": {"home": "홈", "history": "기록", "profile": "프로필"}, "premium": {"premium_cta_title": "멈출 수 없는 존재가 되세요", "premium_cta_description": "프리미엄 기능을 이용해 경험을 향상시키세요", "restore_successful": "구독이 성공적으로 복원되었습니다. 이제 모든 프리미엄 기능을 사용할 수 있습니다."}, "faqs": {"0": {"title": "기본 프로젝트란 무엇인가요?", "description": "기본 프로젝트는 앱 개발 프로세스를 시작하는 데 도움을 주는 무료 모바일 앱 템플릿입니다."}, "1": {"title": "기본 프로젝트의 기능은 무엇인가요?", "description": "RevenueCat, AdMob, Firebase 등 다양한 기능이 포함되어 있습니다."}, "2": {"title": "프리미엄 회원이 되는 방법은?", "description": "앱 개발 과정에서 세션 상태에서 isPremium을 true로 설정하여 프리미엄 회원이 될 수 있습니다."}, "3": {"title": "광고를 활성화하려면 어떻게 하나요?", "description": "Google AdMob을 설정하고 .env 파일에서 광고 ID 값을 변경해야 합니다. 또한 info.plist 파일에서 앱 ID를 설정해야 합니다."}, "4": {"title": "어떤 언어가 지원되나요?", "description": "다양한 언어가 지원됩니다. 언어 목록은 계속 업데이트되고 있으며, 현재 14개 언어가 지원됩니다."}, "5": {"title": "Firebase를 활성화하려면 어떻게 하나요?", "description": "Firebase 계정이 필요하며, 앱에 Firebase를 설정해야 합니다. FlutterFire CLI는 앱에 Firebase를 설정하는 데 도움이 되는 권장 커맨드라인 도구입니다."}, "6": {"title": "어떤 테마가 지원되나요?", "description": "ForUI가 제공하는 모든 기본 테마가 지원됩니다."}, "7": {"title": "인증을 지원하나요?", "description": "현재는 로컬 인증만 지원합니다. Firebase Auth 또는 기타 인증 서비스를 통합할 수 있습니다. vid_firebase_wrapper 패키지가 권장됩니다."}}, "share_text": "안녕! {}를 사용 중이야—거의 완성된 프로젝트 같아. 직접 사용해보고 마법을 느껴봐!"}}