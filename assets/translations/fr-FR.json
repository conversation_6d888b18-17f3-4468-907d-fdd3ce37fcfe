{"vid_base_project": {"splash": {"description": "Meilleur projet de base"}, "quick_actions": {"contact_us": {"title": "🤔 Be<PERSON>in d’aide ?", "subtitle": "<PERSON>ez-nous, notre équipe vous aidera rapidement."}, "special_offer": {"title": "🎉 Offre spéciale pour vous !", "subtitle": "Obtenez un accès complet avec 90 % de réduction."}}, "onboarding": {"welcome_step": {"title": "Bienvenue dans le Projet de Base", "description": "Développez votre application en quelques minutes avec le Projet de Base"}, "ready_to_use_step": {"title": "<PERSON>rêt à l’emploi", "description": "Déployez rapidement après quelques personnalisations"}, "personalization_step": {"title": "Personnalisation", "description": "Activez le suivi pour une expérience personnalisée", "steps": {"0": {"title": "Fonctionnalité personnalisée 1", "description": "Ceci est une description de la fonctionnalité personnalisée 1"}, "1": {"title": "Fonctionnalité personnalisée 2", "description": "Ceci est une description de la fonctionnalité personnalisée 2"}, "2": {"title": "Fonctionnalité personnalisée 3", "description": "Ceci est une description de la fonctionnalité personnalisée 3"}, "3": {"title": "Fonctionnalité personnalisée 4", "description": "Ceci est une description de la fonctionnalité personnalisée 4"}}}, "rate_step": {"title": "Ce que disent les développeurs", "description": "Rejoignez des milliers de développeurs satisfaits qui adorent notre projet de base"}, "analyze_step": {"analyzing_title": "<PERSON><PERSON><PERSON> du profil", "analyzed_title": "Profil analysé", "steps": {"0": {"title": "Étape d’analyse 1", "description": "Ceci est une description de l’étape d’analyse 1"}, "1": {"title": "Étape d’analyse 2", "description": "Ceci est une description de l’étape d’analyse 2"}, "2": {"title": "Étape d’analyse 3", "description": "Ceci est une description de l’étape d’analyse 3"}, "3": {"title": "Étape d’analyse 4", "description": "Ceci est une description de l’étape d’analyse 4"}}}}, "bottom_navigation": {"home": "Accueil", "history": "Historique", "profile": "Profil"}, "premium": {"premium_cta_title": "Soyez inarrêtable", "premium_cta_description": "Obtenez des fonctionnalités premium et améliorez votre expérience", "restore_successful": "Abonnement restauré avec succès. Vous pouvez maintenant utiliser toutes les fonctionnalités premium."}, "faqs": {"0": {"title": "Qu’est-ce que le Projet de Base ?", "description": "Le Projet de Base est un modèle d’application mobile gratuit qui vous aide à démarrer le processus de développement de votre application."}, "1": {"title": "Quelles sont les fonctionnalités du Projet de Base ?", "description": "De nombreuses fonctionnalités comme RevenueCat, AdMob, Firebase, et plus encore."}, "2": {"title": "Comment devenir premium ?", "description": "Pendant le processus de développement de l’application, vous pouvez devenir premium en définissant isPremium à true dans l’état de la session."}, "3": {"title": "Comment activer les publicités ?", "description": "<PERSON><PERSON> de<PERSON> configurer Google AdMob et modifier les valeurs des identifiants publicitaires dans le fichier .env. V<PERSON> devez également définir l’identifiant de votre application dans le fichier info.plist."}, "4": {"title": "Quelles langues sont prises en charge ?", "description": "De nombreuses langues sont prises en charge. Nous mettons à jour la liste des langues régulièrement. Pour l’instant, 14 langues sont prises en charge."}, "5": {"title": "Comment activer Firebase ?", "description": "Vous devez avoir un compte Firebase et configurer Firebase dans votre application. FlutterFire CLI est un outil en ligne de commande recommandé pour configurer Firebase dans votre application."}, "6": {"title": "Quels thèmes sont pris en charge ?", "description": "Tous les thèmes par défaut fournis par ForUI sont pris en charge."}, "7": {"title": "Prenez-vous en charge l’authentification ?", "description": "Pour l’instant, nous ne prenons en charge que l’authentification locale. <PERSON><PERSON> pou<PERSON> intégrer Firebase Auth ou tout autre service d’authentification. Le paquet vid_firebase_wrapper est recommandé."}}, "share_text": "Salut ! J’utilise {}—c’est comme avoir un projet presque prêt. Essaye-le et découvre la magie par toi-même !"}}