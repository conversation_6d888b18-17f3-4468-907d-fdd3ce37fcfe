{"vid_base_project": {"splash": {"description": "En İyi Temel Proje"}, "quick_actions": {"contact_us": {"title": "🤔 Yardıma mı ihtiyacınız var?", "subtitle": "Bize ulaşın, ekibimiz size hızlıca yardımcı olacaktır."}, "special_offer": {"title": "🎉 Size Özel Teklif!", "subtitle": "Tam erişimi %90 indirimle elde edin."}}, "onboarding": {"welcome_step": {"title": "<PERSON><PERSON> Proje’<PERSON> Hoş Geldiniz", "description": "Temel Proje ile uygulamanızı dakikalar içinde geliştirin"}, "ready_to_use_step": {"title": "Kullanıma <PERSON>", "description": "Küçük özelleştirmelerden sonra hızlıca dağıtın"}, "personalization_step": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kişiselleş<PERSON>rilmiş bir den<PERSON>im i<PERSON> takibi et<PERSON>", "steps": {"0": {"title": "Kişiselleştirilmiş Özellik 1", "description": "<PERSON><PERSON>, kişiselleştirilmiş özellik açıklaması 1’dir"}, "1": {"title": "Kişiselleştirilmiş Özellik 2", "description": "<PERSON><PERSON>, kişiselleştirilmiş özellik açıklaması 2’dir"}, "2": {"title": "Kişiselleştirilmiş Özellik 3", "description": "Bu, kişiselleştirilmiş özellik açıklaması 3’tür"}, "3": {"title": "Kişiselleştirilmiş Özellik 4", "description": "Bu, kişiselleştirilmiş özellik açıklaması 4’tür"}}}, "rate_step": {"title": "Geliştiriciler Ne Diyor", "description": "Temel projemizi seven binlerce memnun geliştiriciye katılın"}, "analyze_step": {"analyzing_title": "<PERSON><PERSON>", "analyzed_title": "<PERSON><PERSON>", "steps": {"0": {"title": "Analiz Adımı 1", "description": "<PERSON><PERSON>, analiz adımı açıklaması 1’dir"}, "1": {"title": "Analiz Adımı 2", "description": "<PERSON><PERSON>, analiz adımı açıklaması 2’dir"}, "2": {"title": "Analiz Adımı 3", "description": "<PERSON><PERSON>, analiz adımı açıklaması 3’tür"}, "3": {"title": "Analiz Adımı 4", "description": "<PERSON><PERSON>, analiz adımı açıklaması 4’tür"}}}}, "bottom_navigation": {"home": "<PERSON>", "history": "Geçmiş", "profile": "Profil"}, "premium": {"premium_cta_title": "Durdurulamaz Ol", "premium_cta_description": "Premium özelliklere sahip ol, deneyimini geliştir", "restore_successful": "Abonelik başarıyla geri yüklendi. Artık tüm premium özellikleri kullanabilirsiniz."}, "faqs": {"0": {"title": "Temel Proje Nedir?", "description": "<PERSON><PERSON>, uygulama geliştirme sürecinizi başlatmanıza yardımcı olan ücretsiz bir mobil uygulama şablonudur."}, "1": {"title": "Temel Proje’nin özellikleri nelerdir?", "description": "RevenueCat, AdMob, Firebase gibi birçok özellik ve daha fazlası."}, "2": {"title": "Kendimi premium nasıl yaparım?", "description": "Uygulama gel<PERSON>, oturum durumunda isPremium’u true olarak ayarlayarak premium olabilirsiniz."}, "3": {"title": "Reklamlar nasıl etkinleştirilir?", "description": "Google AdMob’u ayarlamanız ve .env dosyasındaki reklam kimliği değerlerini değiştirmeniz gerekiyor. Ayrıca info.plist dosyasında uygulama kimliğinizi ayarlamanız gerekir."}, "4": {"title": "Hangi diller destekleniyor?", "description": "Birçok dil destekleniyor. Dil listesini sürekli güncelliyoruz. Şu anda 14 dil destekleniyor."}, "5": {"title": "Firebase nasıl etkinleştirilir?", "description": "Bir Firebase hesabına ihtiyacınız var ve uygulamanızda Firebase’ı ayarlamalısınız. FlutterFire CLI, uygulamanızda Firebase’ı ayarlamanıza yardımcı olan önerilen bir komut satırı aracıdır."}, "6": {"title": "Hangi temalar destekleniyor?", "description": "ForUI tarafından sağlanan tüm varsayılan temalar destekleniyor."}, "7": {"title": "Kimlik doğrulama destekleniyor mu?", "description": "<PERSON><PERSON> anda yalnızca yerel kimlik doğrulama destekleniyor. Firebase Auth veya başka bir kimlik doğrulama servisini entegre edebilirsiniz. vid_firebase_wrapper paketi önerilir."}}, "share_text": "<PERSON><PERSON><PERSON><PERSON>! {} kullan<PERSON>yo<PERSON>—sanki neredeyse hazır bir proje gibi. Deneyin ve büyüyü kendiniz gö<PERSON>ün!"}}