# Flutter Base Project

> **Project Overview:** A comprehensive Flutter starter project with built-in features for localization, theming, navigation, state management, and more. This project serves as a foundation for building robust Flutter applications with best practices and common functionality already implemented.

## 📑 Table of Contents

### Setup & Configuration
- [🚀 Getting Started](#getting-started)
- [🤖 AI-Assisted Development](#-ai-assisted-development)
- [📂 Project Structure](#project-structure)
- [⚙️ Customization](#customization)
  - [📱 Package Name](#package-name)
  - [🖼️ App Icons and Splash Screen](#app-icons-and-splash-screen)
  - [🔧 App Configuration](#app-configuration)

### Integrations
- [🔥 Firebase Integration](#firebase-integration)
- [📢 AdMob Integration](#admob-integration)
- [💰 RevenueCat Integration](#revenuecat-integration)

### Core Features
- [🌐 Localization](#localization)
- [📦 Vid Packages](#vid-packages)
- [🎨 UI Components & Theming](#-ui-components--theming)
- [💾 Local Database](#local-database)
- [🧭 Navigation](#navigation)
- [⚛️ State Management](#state-management)

----

### Development & Deployment
- [🧪 Testing](#testing)
  - [📸 Screenshot Tests](#screenshot-tests)
- [🚢 Deployment](#deployment)
- [📱 Example Screenshots](#-example-screenshots)


### Additional Content
- [✨ Additional Features](#-additional-features)
  - [🔒 Security Features](#-security-features)
  - [🎨 Appearance & Localization](#-appearance--localization)
  - [👥 User Engagement](#-user-engagement)
  - [📞 Support & Legal](#-support--legal)
  - [🗄️ Data Management](#-data-management)
- [🛠️ Utility Scripts](#-utility-scripts)

----

## 🚀 Getting Started

### 📋 Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK
- Android Studio / VS Code
- Git

### 💻 Installation

1. Clone the repository

   ```bash
   git clone https://your-repository-url.git
   cd your-project-name
   ```

2. Install dependencies

   ```bash
   flutter pub get
   ```

3. Run the app

   ```bash
   flutter run
   ```

---

## 🤖 AI-Assisted Development

The Vid Flutter Base Project is designed to work seamlessly with AI-assisted development. If you're using AI tools to help build your app, please refer to the dedicated guide:

### 📄 Vibe Coder Documentation

The [vibe_coder_docs.md](./vibe_coder_docs.md) file contains comprehensive instructions for developing apps using AI assistance. This guide is especially helpful for non-developers who want to build mobile apps without writing code themselves.

The documentation covers:

- **Overview of the AI-assisted development workflow**
- **Step-by-step instructions** for using built-in prompt templates
- **Example workflow** for building an AI Paraphrase App
- **Best practices** for working with AI

To get started with AI-assisted development:

1. Read the [vibe_coder_docs.md](./vibe_coder_docs.md) file
2. Follow the three-step process outlined in the documentation:
   - Complete initial setup tasks manually
   - Generate project-specific tasks using AI
   - Implement each task one by one with AI assistance

> 💡 **Note:** The AI-assisted workflow uses special prompt files ([generate_todo_prompt.md](./generate_todo_prompt.md) and [complete_todo.md](./complete_todo.md)) that contain all the necessary instructions for the AI to understand the project structure and implement features correctly.

---

## 📂 Project Structure

The project follows a **feature-first architecture** with the following structure:

```
lib/
├── bootstrap/            # App initialization code
├── config/               # App configuration
├── features/             # Feature modules
│   ├── bottom_navigation_screens/  # Main app screens
│   ├── intro_screens/    # Onboarding, splash, etc.
│   └── other_screens/    # Additional screens
├── product/              # Shared components
│   ├── common_blocs/     # Global state management
│   ├── constants/        # App constants
│   ├── local_database/   # Local storage
│   ├── routing/          # Navigation
│   ├── utils/            # Utility functions
│   └── widgets/          # Common widgets
└── main.dart            # Entry point
```

---

## ⚙️ Customization

### 📱 Package Name

To change the package name of your app:

1. For IOS: Default package name is `com.authenticator.2fa.offline.secure`
2. For Android: Default package name is `com.authenticator.secure.offline`

Search for this package names in the project and replace it with your own package name.


### 🖼️ App Icons and Splash Screen

1. Replace the app icon:

- Place your app icon at `lib/config/app_icon.png` (recommended size: 1024x1024px)
- Run the following command to generate icons for all platforms:

   ```bash
   flutter pub run flutter_launcher_icons
   ```
   > 💡 **Tip:** Available via `sh_generate_app_icon.sh` located in the root directory

2. Update the Native Splash Screen:

- Edit `flutter_native_splash.yaml` to customize the splash screen
- Run the following command to generate the splash screen:

   ```bash
   flutter pub run flutter_native_splash:create
   ```
   > 💡 **Tip:** Available via `sh_generate_native_splash.sh` located in the root directory

### 🔧 App Configuration

1. Open `lib/config/config.dart` and update the following constants:

   ```dart
   // App information
   const String kAppName = 'Your App Name';
   const String kAppStoreId = 'your-app-store-id';
   const String kAppStoreUrl = 'https://yourappstoreurl.com';

   // Company information
   const String kCompanyName = 'Your Company';
   const String kSupportMail = '<EMAIL>';
   const String kSupportMailPrefix = '#{APP-PREFIX}';

   // Legal URLs
   const String kPrivacyPolicyUrl = 'https://yourcompany.com/privacy';
   const String kTermsOfServiceUrl = 'https://yourcompany.com/terms';
   const String kWebsiteUrl = 'https://yourcompany.com';

   // Other constants
   const String kDeleteConfirmationInput = 'DELETE';
   ```

---

## 🔥 Firebase Integration

### Firebase Services Used

The base project uses the following Firebase services by default:

- **Firebase Analytics**: For tracking user events and app usage
- **Cloud Firestore**: For storing and retrieving application data
- **Firebase Crashlytics**: For monitoring app crashes and errors

### Setup Instructions

1. Install the FlutterFire CLI:

```bash
dart pub global activate flutterfire_cli
```

> ⚠️ **Note:** You may need to add the pub cache bin directory to your PATH

2. Configure Firebase for your project:

```bash
flutterfire configure
```

> 💡 **Tip:** This interactive tool will guide you through connecting your app to Firebase

This will guide you through selecting your Firebase project and platforms (iOS/Android).

### Important: Firestore Configuration

The base project attempts to retrieve AI service configuration from Firestore on startup. For the app to work properly, you need to either:

1. Create the required Firestore document at path `APP_CONFIG/app_config` with the following structure:

```json
{
  "apiKey": {
    "gptApiKey": "your-openai-api-key",
    "geminiApiKey": "your-gemini-api-key",
    "deepSeekApiKey": "your-deepseek-api-key"
  }
}
```

OR

2. Comment out the `_initAiService()` method in `lib/features/intro_screens/splash/splash_mixin.dart` if you don't need AI services

> ⚠️ **Important:** The Firestore path is defined as `kCloudDbAppConfigPath` in `lib/product/constants/app_constants.dart` and the document structure must match the model in `lib/product/cloud_database/models/app_config_model.dart`



---

## 📢 AdMob Integration

1. Create AdMob account and get your app IDs and ad unit IDs

2. Replace GADApplicationIdentifier key value in  `ios/Runner/Info.plist` with your AdMob App ID. You can search for `ca-app-pub-example` in the project and replace it with your AdMob App ID.

3. Update env file names in `example.env.development` and `example.env.production` with `.env.development` and `.env.production`

4. Update your `.env.production` and `.env.development` files with your AdMob IDs:

```
AD_UNIT_IDS_ANDROID_BANNER=ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx
AD_UNIT_IDS_IOS_BANNER=ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx
AD_UNIT_IDS_ANDROID_INTERSTITIAL=ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx
AD_UNIT_IDS_IOS_INTERSTITIAL=ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx
AD_UNIT_IDS_ANDROID_REWARDED=ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx
AD_UNIT_IDS_IOS_REWARDED=ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx
```

> 🔍 **Note:** Base project uses test IDs from the [AdMob documentation](https://developers.google.com/admob/android/test-ads) during development.

---

## 💰 RevenueCat Integration

The project uses RevenueCat for in-app purchases and subscription management.

### 🔄 Setup RevenueCat

1. Create a RevenueCat account at [https://www.revenuecat.com/](https://www.revenuecat.com/)

2. Set up your app in the RevenueCat dashboard and configure your products/subscriptions

3. Get your API keys from the RevenueCat dashboard

4. Update env file names in `example.env.development` and `example.env.production` with `.env.development` and `.env.production`

5. Update your `.env.production` and `.env.development` files with your RevenueCat API keys:

```
REV_CAT_API_KEY_ANDROID=your_android_api_key
REV_CAT_API_KEY_APPLE=your_ios_api_key
```

> 🔐 **Security Note:** Never commit these API keys to public repositories

### 🛒 Using RevenueCat in Your App

The project includes a paywall view that uses RevenueCat for handling purchases. You can find the implementation in `lib/features/other_screens/paywall/paywall_view.dart`.

For this screen to work you need to configure paywall view on revenue cat dashboard. You can find the documentation on [RevenueCat](https://www.revenuecat.com/docs/tools/paywalls).

To navigate to the paywall:

Base project automatically navigates to paywall view on first open of home screen if user is not premium. You can navigate to paywall view manually by using the following code:

```dart
context.navigateToPremium();
```

> 💰 **Premium Feature:** This navigation extension is defined in the project's context extension.

The paywall view handles:
- Displaying available products/subscriptions
- Processing purchases
- Restoring purchases
- Error handling

---

## 🌐 Localization

### 🔤 Adding New Translations

1. Translation files are located in `assets/translations/`

2. Replace localizationPrefix variable with your app prefix in `lib/product/constants/i10n_constants.dart`. This has to be the same as the prefix you used in the translation files. You also have to replace current prefix in the translation files with your app prefix.

3. Add the string path to "LocalizationKeys" located in `lib/product/constants/i10n_constants.dart`:

4. To add a new translation first add string to all translation json files and then add it to "LocalizationKeys" enum.

2. Add new keys to all language files (e.g., `en-US.json`, `tr-TR.json`, etc.)

```json
{
    "your_app_prefix":
    {
  "hello": "Hello",
  "welcome_message": "Welcome to our app",
  "your_new_key": "Your new translation",
  "nested_key": {
    "nested_key_1": "Nested key 1",
    "nested_key_2": "Nested key 2",
  }
}}
```
Here is an example on how to add enum for the above example:
```dart
enum LocalizationKeys {
    hello("hello"),
    welcomeMessage("welcome_message"),
    yourNewKey("your_new_key"),
    nestedKey1("nested_key.nested_key_1"),
    nestedKey2("nested_key.nested_key_2"),
    
}
```

> 💡 **Tip:** If you want to bulk localize a json file you can use The [json_localization_prompt.md](./json_localization_prompt.md) file. Grok
is suggested AI for this task.

### 🗣️ Adding a New Language

#### Flutter App Localization

1. Create a new JSON file in `assets/translations/` with the appropriate locale name (e.g., `fr-FR.json`)

2. Add the new locale to the supported locales list in `lib/product/constants/i10n_constants.dart`:

```dart
const supportedLocales = [
  Locale('en', 'US'),
  Locale('tr', 'TR'),
  // Add your new locale
  Locale('fr', 'FR'),
];
```

#### iOS InfoPlist Localization

The project also supports localization of iOS-specific strings in the Info.plist file:

1. **Add a new language in Xcode:**
   - Open the iOS project in Xcode: `open ios/Runner.xcworkspace`
   - Select the project in the Project Navigator
   - Go to the "Info" tab
   - Under "Localizations", click the "+" button
   - Select your desired language and check "InfoPlist.strings"
   - Click "Finish"

2. **Edit the InfoPlist.strings file:**
   - Navigate to `ios/Runner/[language_code].lproj/InfoPlist.strings`
   - Edit the file to localize iOS-specific strings:

```
"CFBundleDisplayName"="[Localized App Name]";
"NSCameraUsageDescription"="[Localized Camera Permission Text]";
"NSFaceIDUsageDescription"="[Localized Face ID Permission Text]";
"NSPhotoLibraryUsageDescription"="[Localized Photo Library Permission Text]";
"NSUserTrackingUsageDescription"="[Localized Tracking Permission Text]";
```

> 💡 **Note:** Make sure to localize all permission strings to provide a fully localized experience for users. These strings appear in system permission dialogs on iOS devices.

---

## 📦 Vid Packages

The project uses several custom Vid packages that provide core functionality. These packages are all sourced from the Vid-Apps-Development GitHub repository.

### 🔄 Updating Vid Package Versions

The project uses a YAML anchor and reference system to maintain consistent versions across all Vid packages:

```yaml
vid-package-tag: &vid_package_tag v2.1.0
```

> 🔄 **Version Management:** This YAML anchor is referenced by all Vid package dependencies

To update all Vid packages to a new version:

1. Simply change the version number in this single line in `pubspec.yaml` (e.g., from `v2.1.0` to `v2.2.0`)
2. Run `flutter pub get` to fetch the updated packages
3. Test your app thoroughly after updating, as breaking changes may be introduced in new versions

### 📋 Vid Package Overview

Here's a brief explanation of each Vid package dependency:

#### 🧰 Core Packages
- **vid_core**: Core utilities, extensions, and base functionality for all Vid apps
- **vid_ui**: UI components, widgets, and theming system with custom design elements

#### ✨ Feature Packages
- **vid_ad_manager**: Manages AdMob integration, ad loading, and display with error handling
- **vid_ai_service**: Provides AI service integration for text generation and processing
- **vid_firebase_wrapper**: Simplifies Firebase integration with analytics, auth, and cloud services
- **vid_image_manager**: Handles image loading, caching, and processing with optimized performance
- **vid_in_app_review_manager**: Manages app store review prompts with smart timing
- **vid_permission_manager**: Handles device permission requests with user-friendly dialogs
- **vid_purchase_manager**: Manages in-app purchases via RevenueCat with subscription handling
- **vid_secure_app**: Provides biometric and PIN authentication with secure storage
- **vid_share_manager**: Handles content sharing across platforms with customizable templates
- **vid_speech_to_text**: Provides speech recognition capabilities with multiple language support

#### 🛠️ Development Package
- **vid_analysis**: Custom static code analysis rules for maintaining code quality

---

## 🎨 UI Components & Theming

The project uses ForUI for UI components and theming. ForUI is a design system that provides a set of consistent, accessible, and customizable UI components for Flutter applications.

### 🖊️ Design System

ForUI provides a comprehensive design system with the following features:

### 📚 Usage Documentation

For detailed documentation on how to use ForUI components, visit the official documentation:

[ForUI Documentation](https://forui.dev/docs)

The documentation includes:
- Component API references
- Usage examples
- Design guidelines
- Customization options

---

## 💾 Local Database

The project uses a local database for storing user data and app state.

### 🔄 Working with the Database

The project supports two types of database storage:

| Storage Type | Purpose | Best For |
|-------------|---------|----------|
| **Key-Value Storage** | For storing single instances of a model | User settings, app configuration |
| **Table Storage** | For storing collections of models | Chat messages, history items |

#### 📝 Creating Models

1. Create a new model class in `lib/product/local_database/models/`:

```dart
// First, create your model file: your_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'your_model.freezed.dart';
part 'your_model.g.dart';

@freezed
class YourModel with _$YourModel {
  const factory YourModel({
    required String id,
    required String name,
    // Add your fields here
    String? optionalField,
    @Default(false) bool isActive,
    DateTime? createdAt,
    DateTime? lastUpdatedAt,
    DateTime? deletedAt,
  }) = _YourModel;

  factory YourModel.fromJson(Map<String, dynamic> json) => 
      _$YourModelFromJson(json);
}
```

Then run the build_runner to generate the code:
available via sh_build_runner.sh located in the root directory

```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

> 💡 **Tip:** Available via `sh_build_runner.sh` located in the root directory


#### 🔑 Option 1: Key-Value Storage (Single Instance)

1. Add your model to the `KeyValueTables` enum in `lib/product/local_database/local_db_manager.dart`:

```dart
enum KeyValueTables {
  userFlow('userFlow'),
  appUtilities('app_utilities'),
  user('user'),
  // Add your new table
  yourModel('your_model');

  const KeyValueTables(this.value);

  final String value;
}
```

2. Add convenience methods to `LocalDbManager` for your model:

```dart
// Get your model
Future<YourModel?> getYourModel() async =>
    _dbManager.getKeyValue<YourModel>(
      KeyValueTables.yourModel.value,
      fromJson: YourModel.fromJson,
    );

// Update or create your model
Future<YourModel> updateYourModel(YourModel model) async {
  await _dbManager.addKeyValue(
    KeyValueTables.yourModel.value,
    model.toJson(),
  );
  return model;
}

// Delete your model
Future<void> deleteYourModel() async =>
    _dbManager.deleteKeyValue(KeyValueTables.yourModel.value);
```

3. Use the methods in your code:

```dart
// Create or update
final yourModel = YourModel(id: '1', name: 'Test');
await LocalDbManager.instance.updateYourModel(yourModel);

// Retrieve
final retrievedModel = await LocalDbManager.instance.getYourModel();

// Delete
await LocalDbManager.instance.deleteYourModel();
```

#### 📊 Option 2: Table Storage (Collections)

1. Uncomment and add your model to the `DbTables` enum in `lib/product/local_database/local_db_manager.dart`:

```dart
enum DbTables {
  chats('chats'),
  // Add your new table
  yourModels('your_models');

  const DbTables(this.value);

  final String value;
}
```

2. Add CRUD methods to `LocalDbManager` for your collection:

```dart
// Get all items
Future<List<YourModel>> getYourModels() async => _dbManager
    .getAll<YourModel>(DbTables.yourModels.value, YourModel.fromJson);

// Add a new item
Future<YourModel> addYourModel(YourModel model) async {
  final newModel = model.copyWith(
    createdAt: DateTime.now(),
    lastUpdatedAt: DateTime.now(),
  );
  await _dbManager.add(DbTables.yourModels.value, newModel.toJson());
  return newModel;
}

// Update an existing item
Future<YourModel> updateYourModel(YourModel model) async {
  final updatedModel = model.copyWith(
    lastUpdatedAt: DateTime.now(),
  );
  await _dbManager.updateById(
    DbTables.yourModels.value,
    updatedModel.toJson(),
    model.id,
  );
  return updatedModel;
}

// Soft delete (mark as deleted)
Future<YourModel> deleteYourModel(YourModel model) async {
  final deletedModel = model.copyWith(
    deletedAt: DateTime.now(),
    lastUpdatedAt: DateTime.now(),
  );
  await _dbManager.updateById(
    DbTables.yourModels.value,
    deletedModel.toJson(),
    model.id,
  );
  return deletedModel;
}

// Hard delete all items
Future<void> deleteAllYourModels() async =>
    _dbManager.deleteAll(DbTables.yourModels.value);
```

3. Use the methods in your code:

```dart
// Create
final model = YourModel(id: '1', name: 'Test Item');
await LocalDbManager.instance.addYourModel(model);

// Retrieve all
final allModels = await LocalDbManager.instance.getYourModels();

// Update
final updatedModel = allModels.first.copyWith(name: 'Updated Name');
await LocalDbManager.instance.updateYourModel(updatedModel);

// Delete (soft)
await LocalDbManager.instance.deleteYourModel(updatedModel);

// Delete all (hard)
await LocalDbManager.instance.deleteAllYourModels();
```

---

## 🧭 Navigation

The project uses GoRouter for navigation with a structured approach organized by route categories.

### 🗺️ Route Structure

Routes are organized into three main categories:

1. **Initial Routes** (`InitialRoute`): First screens like splash and onboarding
2. **Main Routes** (`MainRoute`): Primary screens accessible via bottom navigation
3. **Other Routes** (`OtherRoute`): Additional screens not in the main navigation flow

### 📱 Bottom Navigation

The main routes are displayed in a bottom navigation bar using `StatefulShellRoute` from GoRouter. The implementation is in `lib/features/bottom_navigation_screens/bottom_navigation_bar_wrapper.dart`.

#### ➕ Adding a Tab to Bottom Navigation

1. Update the `destinations` list in `bottom_navigation_bar_wrapper.dart`:

```dart
List<({String name, IconData icon, int index})> destinations(
    BuildContext context,
  ) => [
    // Existing destinations
    (
      name: LocalizationKeys.bottomNavigationYourTab.tr(context: context),
      icon: FIcons.yourIcon,
      index: 3, // Next available index
    ),
  ];
```

2. Add a translation key in your localization files and `i10n_constants.dart`

3. Add a new `StatefulShellBranch` in `lib/product/routing/paths/main_route/main_route.dart`:

```dart
StatefulShellBranch(
  routes: [
    GoRoute(
      path: MainRoutePaths.yourTab.path,
      name: MainRoutePaths.yourTab.name,
      builder: (context, state) => const YourTabView(),
    ),
  ],
),
```

### 🛣️ Adding a New Route

1. Add the route path to the appropriate route paths enum:

```dart
// For initial routes: lib/product/routing/paths/intro_route/intro_route_paths.dart
enum InitialRoutePaths {
  // Existing paths
  yourNewScreen('/your-new-screen'),
  
  // Constructor and other methods...
}

// OR for main routes: lib/product/routing/paths/main_route/main_route_paths.dart
enum MainRoutePaths {
  // Existing paths
  yourNewScreen('/your-new-screen'),
  
  // Constructor and other methods...
}

// OR for other routes: lib/product/routing/paths/other_route/other_route_paths.dart
enum OtherRoutePaths {
  // Existing paths
  yourNewScreen('/your-new-screen'),
  
  // Constructor and other methods...
}
```

2. Add the route to the appropriate route class:

```dart
// For initial routes: lib/product/routing/paths/intro_route/intro_route.dart
static List<RouteBase> routes = [
  // Existing routes
  GoRoute(
    path: InitialRoutePaths.yourNewScreen.path,
    name: InitialRoutePaths.yourNewScreen.name,
    builder: (context, state) => YourNewScreenView(),
  ),
];

// OR for main routes if it's a top-level tab: lib/product/routing/paths/main_route/main_route.dart
// Add a new StatefulShellBranch for a new tab, or add to an existing branch for nested navigation

// OR for other routes: lib/product/routing/paths/other_route/other_route.dart
static List<RouteBase> routes = [
  // Existing routes
  GoRoute(
    path: OtherRoutePaths.yourNewScreen.path,
    name: OtherRoutePaths.yourNewScreen.name,
    builder: (context, state) => YourNewScreenView(),
  ),
];
```

3. Navigate to your route:

```dart
// For initial routes
context.pushNamed(InitialRoutePaths.yourNewScreen.name);

// For main routes
context.pushNamed(MainRoutePaths.yourNewScreen.name);

// For other routes
context.pushNamed(OtherRoutePaths.yourNewScreen.name);
```


---

## ⚛️ State Management

The project uses the BLoC pattern for state management, with Cubit for simpler state management cases.

### 🧩 Creating a New Cubit

1. Create a new directory for your feature in `lib/features/your_feature/`

2. Create the Cubit files:

```
lib/features/your_feature/
├── cubit/
│   ├── your_feature_cubit.dart
│   └── your_feature_state.dart
├── your_feature_view.dart
└── your_feature_mixin.dart
```

3. Implement your Cubit:

#### 🔄 Example 1: Simple Toggle Boolean

```dart
// toggle_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'toggle_state.freezed.dart';

@freezed
class ToggleState with _$ToggleState {
  const factory ToggleState({@Default(false) bool isEnabled}) = _ToggleState;
}

// toggle_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'toggle_state.dart';

class ToggleCubit extends Cubit<ToggleState> {
  ToggleCubit() : super(const ToggleState());
  
  void toggle() {
    emit(ToggleState(isEnabled: !state.isEnabled));
  }
}
```

Usage in a widget:

```dart
class ToggleView extends StatelessWidget {
  const ToggleView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ToggleCubit(),
      child: Scaffold(
        appBar: AppBar(title: const Text('Toggle Example')),
        body: Center(
          child: BlocBuilder<ToggleCubit, ToggleState>(
            builder: (context, state) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Current status: ${state.isEnabled ? "ON" : "OFF"}',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => context.read<ToggleCubit>().toggle(),
                    child: const Text('Toggle'),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
```

#### 🔄 Example 2: Complex State Management

```dart
// your_feature_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'your_feature_state.freezed.dart';

@freezed
class YourFeatureState with _$YourFeatureState {
  const factory YourFeatureState.initial() = _Initial;
  const factory YourFeatureState.loading() = _Loading;
  const factory YourFeatureState.loaded({required String data}) = _Loaded;
  const factory YourFeatureState.error({required String message}) = _Error;
}

// your_feature_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'your_feature_state.dart';

class YourFeatureCubit extends Cubit<YourFeatureState> {
  YourFeatureCubit() : super(const YourFeatureState.initial());
  
  Future<void> loadData() async {
    emit(const YourFeatureState.loading());
    try {
      // Fetch data or perform operations
      final data = await _fetchData();
      emit(YourFeatureState.loaded(data: data));
    } catch (e) {
      emit(YourFeatureState.error(message: e.toString()));
    }
  }
  
  Future<String> _fetchData() async {
    // Implementation
    return 'Your data';
  }
}
```

4. Use the Cubit in your widget:

```dart
// your_feature_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'cubit/your_feature_cubit.dart';
import 'cubit/your_feature_state.dart';

class YourFeatureView extends StatelessWidget {
  const YourFeatureView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => YourFeatureCubit()..loadData(),
      child: Scaffold(
        appBar: AppBar(title: const Text('Your Feature')),
        body: BlocBuilder<YourFeatureCubit, YourFeatureState>(
          builder: (context, state) {
            return state.when(
              initial: () => const Center(child: Text('Initial State')),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (data) => Center(child: Text(data)),
              error: (message) => Center(child: Text('Error: $message')),
            );
          },
        ),
      ),
    );
  }
}
```

## ✨ Additional Features

The base project includes several ready-to-use features that can be found in the profile screen. These features are implemented in `lib/features/bottom_navigation_screens/profile/profile_view.dart` and `profile_mixin.dart`.

### 🔒 Security Features

1. **Biometric Authentication**
   - Toggle Face ID/Touch ID for app access
   - Implementation: `secureAppController.toggleFaceId(context)`
   - Usage: Call this method to enable/disable biometric authentication

2. **PIN Lock**
   - Set, update, or remove PIN for app access
   - Implementation: `secureAppController.updatePinLock(context, isRemove: false)`
   - Usage: Call this method to manage PIN authentication

3. **Advanced Security**
   - Additional security settings screen
   - Navigation: `context.pushNamed(OtherRoutePaths.advancedSecurity.name)`

4. **Security Checks**
   - Screen to verify device security status
   - Navigation: `context.pushNamed(OtherRoutePaths.securityCheck.name)`

### 🎨 Appearance & Localization

1. **Theme Selection**
   - Change between light, dark, and system themes
   - Implementation: `showVidThemePicker()` and `appNotifier.updateAppUtil()`
   - Usage: Call these methods to show theme picker and update the selected theme

2. **Language Selection**
   - Change app language from supported locales
   - Implementation: `showVidSingleSelectSheet<Locale>()` and `context.setLocale()`
   - Usage: Call these methods to show language picker and update the selected language

### 👥 User Engagement

1. **Quick Actions**
   - Provide shortcuts for common actions from the app icon
   - Implementation: `QuickActionsManager().initialize()`
   - Usage: Initialize in app startup to enable quick actions
   - Customization: Modify `actions` list in `QuickActionsManager` class
   - Default actions: Contact Us and Special Offer
   - Location: `lib/product/managers/quick_actions_manager.dart`

2. **Rate App**
   - Open app store for rating
   - Implementation: `VidInAppReviewManager.instance.openStore(appStoreId: kAppStoreId)`
   - Usage: Call this method to prompt users to rate the app

3. **Share App**
   - Share app with others via platform share dialog
   - Implementation: `VidShareManager.shareAppUrl()`
   - Usage: Call this method to show the share dialog with app information

4. **Our Apps**
   - Showcase other apps from your company
   - Navigation: `context.pushNamed(OtherRoutePaths.ourApps.name)`

5. **Feature Request**
   - Allow users to request new features
   - Navigation: `context.pushNamed(OtherRoutePaths.contactUs.name, extra: VidContactUsSubject.featureRequest)`

### 📞 Support & Legal

1. **FAQs**
   - Frequently asked questions screen
   - Navigation: `context.pushNamed(OtherRoutePaths.faq.name)`

2. **Contact Us**
   - Contact form for user support
   - Navigation: `context.pushNamed(OtherRoutePaths.contactUs.name)`

3. **Privacy Policy & Terms of Service**
   - Open privacy policy or terms of service in web browser
   - Implementation: `launchUrl(Uri.parse(url))`
   - Usage: Call this method with the appropriate URL to open legal documents

### 🗄️ Data Management

1. **Clear Local Data**
   - Reset app by clearing all local data
   - Implementation: `LocalDbManager.instance.dropDb()`
   - Usage: Call this method after confirmation to clear all local data

### 📝 How to Use These Features

All these features are ready to use in the base project. The implementations can be found in:

- `lib/features/bottom_navigation_screens/profile/profile_view.dart`: UI implementation
- `lib/features/bottom_navigation_screens/profile/profile_mixin.dart`: 

---

## 🛠️ Utility Scripts

The project includes several utility shell scripts in the root directory to simplify common development tasks:

### 📋 Available Scripts

| Script | Purpose | Description |
|--------|---------|-------------|
| `sh_build_runner.sh` | Code Generation | Runs the build_runner to generate code for freezed models, JSON serialization, etc. |
| `sh_generate_app_icon.sh` | Asset Generation | Generates app icons for all platforms based on your app icon image |
| `sh_generate_native_splash.sh` | Asset Generation | Creates the native splash screen based on your configuration |
| `sh_generate_screenshots.sh` | Testing & Store Assets | Runs the integration tests to capture screenshots for all locales |
| `sh_ios_clean.sh` | iOS Development | Cleans iOS pods and reinstalls them to fix common iOS build issues |

### 🚀 How to Use

To run any of these scripts, open a terminal in the project root directory and execute:

```bash
# Make the script executable (only needed once)
chmod +x script_name.sh

# Run the script
./script_name.sh
```

Alternatively, you can run them directly with:

```bash
bash script_name.sh
```

> 💡 **Tip:** These scripts automate common tasks that would otherwise require typing long commands or performing multiple steps manually.

---

---

## 🚢 Deployment

### 📸 Generate Screenshots

The project includes a screenshot testing framework to capture screenshots of your app in different locales for app store submission.

1. Add a new screen to the screenshot test in `integration_test/screenshot_test.dart`:

```dart
final items = [
  // Existing items
  ScreenshotScreenItem(
    screenshotName: 'your_screen',
    builder: (context) => YourScreen(),
  ),
];
```

2. Run the screenshot test:

```bash
flutter drive \
  --driver=test_driver/integration_test.dart \
  --target=integration_test/screenshot_test.dart
```

> 💡 **Tip:** Available via `sh_generate_screenshots.sh` located in the root directory

---

## 📱 Example Screenshots

Here are some example screenshots from the app (English version):

### 🏠 Home Screen

<img src="screenshots/en_US/home.png" width="250">

### 👤 Profile Screen

<img src="screenshots/en_US/profile.png" width="250">

### 📜 History Screen

<img src="screenshots/en_US/history.png" width="250">

### 🔒 Security Checks Screen

<img src="screenshots/en_US/security-checks.png" width="250">
