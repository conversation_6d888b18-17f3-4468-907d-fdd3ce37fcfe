## 🔍 Legend
- [x] Completed
- [ ] Uncompleted

## 🚀 Initial Setup Tasks

### 📱 Project Configuration
- [x] **Update Package Name**
  - Replace default package names (`com.authenticator.2fa.offline.secure`) with your own
  - Search and replace in both iOS and Android configurations

- [x] **Configure App Constants**
  - Update in `lib/config/config.dart`:
    - App name, store IDs, company details
    - Support email, legal URLs

- [x] **Update Example Env Files**
  - Update env file names in `example.env.development` and `example.env.production` with `.env.development` and `.env.production`
  - Update env file values in `.env.development` and `.env.production` with your own values

- [x] **Update Localization**
  - Set your app's localization prefix in all translation JSON files
  - Update prefix in `lib/product/constants/i10n_constants.dart`

### 🎨 Visual Assets
- [x] **Generate App Icon**
  - Place icon at `lib/config/app_icon.png` (1024×1024px)
  - Run `sh_generate_app_icon.sh` script

- [x] **Generate Native Splash Screen**
  - Customize `flutter_native_splash.yaml`
  - Run `sh_generate_native_splash.sh` script

### 🔌 Service Integrations
- [x] **Set Up Firebase**
  - Run `flutterfire configure` to connect to Firebase
  - Configure Analytics, Firestore, and Crashlytics

- [x] **Configure or Disable Firestore for AI**
  - Option 1: Create `APP_CONFIG/app_config` document with API keys
  - Option 2: Comment out `_initAiService()` in splash_mixin.dart

- [x] **Set Up AdMob**
  - Replace test ad unit IDs in `.env` files
  - Update AdMob app ID in platform configurations

- [x] **Set Up RevenueCat**
  - Add API keys to `.env` files
  - Configure products in RevenueCat dashboard

### 📦 Project Specific Tasks

#### 🧠 AI Paraphrase Core Functionality
- [ ] **Define AI Paraphrase Feature Architecture**
  - Create feature folder structure in `lib/features` by following existing feature structure
  - Design data models using Freezed for paraphrase requests/responses
  - Implement error handling for interactions
  - Ensure all data processing follows local-only pattern

- [ ] **Implement AI Service Integration**
  - Implement ai functionalities via `vid_ai_service`

- [ ] **Create Paraphrase Input UI**
  - Design text input screen with ForUI components
  - Add text formatting options (clear, paste, copy)
  - Implement character/word count display
  - Create loading states using  `flutter_spinkit` . It's accessible via `vid_ui` package.

- [ ] **Implement Paraphrase Output UI**
  - Design results display with multiple paraphrase options
  - Add copy, share, and save functionality
  - Implement history tracking for previous paraphrases (stored locally using `LocalDbManager`)
  - Create comparison view between original and paraphrased text

#### 🔄 Paraphrase Options & Customization
- [ ] **Add Paraphrase Style Options**
  - Implement style selection (formal, casual, creative, simple)
  - Create tone adjustment options (professional, friendly, academic)
  - Add length control (shorter, similar, longer)
  - Design UI for style customization using ForUI components
  - Store user preferences locally using `LocalDbManager`

- [ ] **Create Language Selection**
  - Implement language detection for input text
  - Add target language selection for paraphrasing
  - Support cross-language paraphrasing
  - Update localization for supported languages
  - Cache language models locally when possible

#### 💾 Local Data Management
- [ ] **Implement Local Storage Architecture**
  - Create database schema for saved paraphrases using `LocalDbManager`
  - Implement Freezed models for database entities
  - Add CRUD operations for paraphrase history
  - Ensure all data remains on device with no cloud sync

- [ ] **Add Data Export & Backup**
  - Create JSON/CSV export functionality for paraphrase history
  - Implement local backup and restore options
  - Add data cleanup utilities and storage management
  - Provide options to delete history or specific entries

- [ ] **Implement User Preferences**
  - Add privacy options for data retention periods
  - Store all preferences using `LocalDbManager`

#### 🔐 Privacy & Security
- [ ] **Enhance Local Privacy**
  - Add option to exclude paraphrase history from device backups

#### 📱 App Experience
- [ ] **Create Onboarding Flow**
  - Use existing `onboarding_view` and `onboarding_mixin`
  - Design welcome screens with app introduction
  - Ask user how they find the app (social media, search, etc.)
  - Make user select their main usage purpose (personal, professional, etc.)
  - Highlight privacy benefits of local-only storage
  - Ask for App Tracking Transparency (ATT) permission
  - Ask for Rate us 
  - Display `vid_analyze_view` to dipslay user that we are generationg their profile 

- [ ] **Implement Navigation**
  - Set up `GoRouter` configuration with appropriate routes
  - Follow existing navigation structure
  - Add routes to appropriate route paths enum
  - Implement bottom navigation with home,history, favorites, and settings

#### 📊 Performance Optimization
- [ ] **Optimize App Performance**
  - Optimize UI rendering with `const` constructors
