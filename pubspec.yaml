name: vid_base_project
description: "A new Flutter project."
publish_to: 'none' 


version: 1.0.0+1

environment:
  sdk: ^3.8.0
  flutter: ^3.32.0

vid-package-tag: &vid_package_tag v2.1.0

dependencies:
  flutter:
    sdk: flutter
  flutter_dotenv: ^5.2.1
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.4.5
  quick_actions: ^1.1.0
  vid_ad_manager:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_ad_manager
      ref: *vid_package_tag
  vid_ai_service:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_ai_service
      ref: *vid_package_tag
  vid_core:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_core
      ref: *vid_package_tag
  vid_firebase_wrapper:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_firebase/vid_firebase_wrapper
      ref: *vid_package_tag
  vid_image_manager:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_image_manager
      ref: *vid_package_tag
  vid_in_app_review_manager:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_in_app_review_manager
      ref: *vid_package_tag
  vid_permission_manager:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_permission_manager
      ref: *vid_package_tag
  vid_purchase_manager:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_purchase_manager
      ref: *vid_package_tag
  vid_secure_app:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_secure_app
      ref: *vid_package_tag
  vid_share_manager:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_share_manager
      ref: *vid_package_tag
  vid_speech_to_text:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_speech_to_text
      ref: *vid_package_tag
  vid_ui:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_ui
      ref: *vid_package_tag

dependency_overrides:
  freezed: ^3.0.6
  freezed_annotation: ^3.0.0

dev_dependencies:
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  freezed: ^3.0.6
  integration_test:
    sdk: flutter
  json_serializable: ^6.9.5
  vid_analysis:
    git:
      url: **************:Vid-Apps-Development/vid_packages_v2.git
      path: packages/vid_analysis
      ref: *vid_package_tag

flutter:
  uses-material-design: true

  assets:
    - .env.development
    - .env.production
    - assets/translations/
